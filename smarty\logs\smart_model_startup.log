OptimizedSQLiteBus not available, falling back to standard SQLiteBus
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 248, in setup_strategy_logging
    logger = get_strategy_logger(strategy_name, log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 243, in get_strategy_logger
    return StrategyLogger(strategy_name, log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 82, in __init__
    self.info(f"\U0001f680 Strategy logger initialized for {strategy_name}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f680 Strategy logger initialized for smart_model_integrated'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 248, in setup_strategy_logging
    logger = get_strategy_logger(strategy_name, log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 243, in get_strategy_logger
    return StrategyLogger(strategy_name, log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 82, in __init__
    self.info(f"\U0001f680 Strategy logger initialized for {strategy_name}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f680 Strategy logger initialized for smart_model_integrated'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4c1' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 248, in setup_strategy_logging
    logger = get_strategy_logger(strategy_name, log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 243, in get_strategy_logger
    return StrategyLogger(strategy_name, log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 83, in __init__
    self.info(f"\U0001f4c1 Log file: {self.log_file}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4c1 Log file: logs\\20250529_12_smart_model_integrated.log'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4c1' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 248, in setup_strategy_logging
    logger = get_strategy_logger(strategy_name, log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 243, in get_strategy_logger
    return StrategyLogger(strategy_name, log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 83, in __init__
    self.info(f"\U0001f4c1 Log file: {self.log_file}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4c1 Log file: logs\\20250529_12_smart_model_integrated.log'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4ca' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 248, in setup_strategy_logging
    logger = get_strategy_logger(strategy_name, log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 243, in get_strategy_logger
    return StrategyLogger(strategy_name, log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 84, in __init__
    self.info(f"\U0001f4ca JSON events: {json_log_file}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4ca JSON events: logs\\20250529_12_smart_model_integrated_events.json'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4ca' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 248, in setup_strategy_logging
    logger = get_strategy_logger(strategy_name, log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 243, in get_strategy_logger
    return StrategyLogger(strategy_name, log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 84, in __init__
    self.info(f"\U0001f4ca JSON events: {json_log_file}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4ca JSON events: logs\\20250529_12_smart_model_integrated_events.json'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3af' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 251, in setup_strategy_logging
    logger.info(f"\U0001f3af Strategy: {strategy_name}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f3af Strategy: smart_model_integrated'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3af' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 251, in setup_strategy_logging
    logger.info(f"\U0001f3af Strategy: {strategy_name}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f3af Strategy: smart_model_integrated'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4c5' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 252, in setup_strategy_logging
    logger.info(f"\U0001f4c5 Session: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4c5 Session: 2025-05-29 12:48:18'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4c5' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 252, in setup_strategy_logging
    logger.info(f"\U0001f4c5 Session: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4c5 Session: 2025-05-29 12:48:18'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f527' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 253, in setup_strategy_logging
    logger.info(f"\U0001f527 Log level: {log_level}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f527 Log level: INFO'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f527' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 253, in setup_strategy_logging
    logger.info(f"\U0001f527 Log level: {log_level}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f527 Log level: INFO'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4bb' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 254, in setup_strategy_logging
    logger.info(f"\U0001f4bb Python: {sys.version}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4bb Python: 3.13.2 | packaged by Anaconda, Inc. | (main, Feb  6 2025, 18:49:14) [MSC v.1929 64 bit (AMD64)]'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4bb' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 254, in setup_strategy_logging
    logger.info(f"\U0001f4bb Python: {sys.version}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4bb Python: 3.13.2 | packaged by Anaconda, Inc. | (main, Feb  6 2025, 18:49:14) [MSC v.1929 64 bit (AMD64)]'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4c1' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 255, in setup_strategy_logging
    logger.info(f"\U0001f4c1 Working directory: {os.getcwd()}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4c1 Working directory: C:\\Users\\<USER>\\Documents\\dev\\smarty'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4c1' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 53, in <module>
    logger = get_smart_model_logger()
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 263, in get_smart_model_logger
    return setup_strategy_logging("smart_model_integrated", log_level)
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 255, in setup_strategy_logging
    logger.info(f"\U0001f4c1 Working directory: {os.getcwd()}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4c1 Working directory: C:\\Users\\<USER>\\Documents\\dev\\smarty'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f41b' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2233, in <module>
    logger.info(f"\U0001f41b Debug mode enabled")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f41b Debug mode enabled'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f41b' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2233, in <module>
    logger.info(f"\U0001f41b Debug mode enabled")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f41b Debug mode enabled'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4ca' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2234, in <module>
    logger.info(f"\U0001f4ca Strategy: {args.strategy}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4ca Strategy: smart_integrated'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4ca' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2234, in <module>
    logger.info(f"\U0001f4ca Strategy: {args.strategy}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4ca Strategy: smart_integrated'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4b0' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2235, in <module>
    logger.info(f"\U0001f4b0 Symbol: {args.symbol}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4b0 Symbol: BTC-USDT'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4b0' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2235, in <module>
    logger.info(f"\U0001f4b0 Symbol: {args.symbol}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4b0 Symbol: BTC-USDT'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f9ea' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2236, in <module>
    logger.info(f"\U0001f9ea Testnet: {args.testnet}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f9ea Testnet: False'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f9ea' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2236, in <module>
    logger.info(f"\U0001f9ea Testnet: {args.testnet}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f9ea Testnet: False'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4b8' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2237, in <module>
    logger.info(f"\U0001f4b8 Trading: {args.trading}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4b8 Trading: False'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4b8' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2237, in <module>
    logger.info(f"\U0001f4b8 Trading: {args.trading}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f4b8 Trading: False'
Arguments: ()
2025-05-29 12:48:18,168 - strategy.smart_model_integrated - INFO - Initialized message bus: SQLiteBus
2025-05-29 12:48:18,168 - strategy.smart_model_integrated - INFO - Set HTX client simulation mode: True
2025-05-29 12:48:18,168 - strategy.smart_model_integrated - INFO - Set publisher for HTX client
2025-05-29 12:48:18,168 - strategy.smart_model_integrated - INFO - Set publisher for Multi-Exchange client
2025-05-29 12:48:18,168 - strategy.smart_model_integrated - INFO - Set publisher for Binance fallback client
arch package not available, falling back to statsmodels
Neither arch nor statsmodels available, using simple volatility estimation
2025-05-29 12:48:18,185 - strategy.smart_model_integrated - WARNING - SignalStar client not initialized, social sentiment model disabled
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2049, in run_orchestrator
    orchestrator = Orchestrator(config)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 96, in __init__
    self._init_components()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 265, in _init_components
    logger.info("\u2705 Enhanced LLM Consumer initialized successfully")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\u2705 Enhanced LLM Consumer initialized successfully'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2049, in run_orchestrator
    orchestrator = Orchestrator(config)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 96, in __init__
    self._init_components()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 265, in _init_components
    logger.info("\u2705 Enhanced LLM Consumer initialized successfully")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\u2705 Enhanced LLM Consumer initialized successfully'
Arguments: ()
2025-05-29 12:48:18,203 - strategy.smart_model_integrated - INFO - Starting orchestrator...
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f504' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2053, in run_orchestrator
    await orchestrator.start()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 296, in start
    connection_success = await self._try_primary_connection()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 436, in _try_primary_connection
    logger.info("\U0001f504 Attempting Multi-Exchange connection...")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f504 Attempting Multi-Exchange connection...'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f504' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2053, in run_orchestrator
    await orchestrator.start()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 296, in start
    connection_success = await self._try_primary_connection()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 436, in _try_primary_connection
    logger.info("\U0001f504 Attempting Multi-Exchange connection...")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f504 Attempting Multi-Exchange connection...'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2053, in run_orchestrator
    await orchestrator.start()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 296, in start
    connection_success = await self._try_primary_connection()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 440, in _try_primary_connection
    logger.info("\u2705 Connected to Multi-Exchange client")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\u2705 Connected to Multi-Exchange client'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2053, in run_orchestrator
    await orchestrator.start()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 296, in start
    connection_success = await self._try_primary_connection()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 440, in _try_primary_connection
    logger.info("\u2705 Connected to Multi-Exchange client")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\u2705 Connected to Multi-Exchange client'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2053, in run_orchestrator
    await orchestrator.start()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 320, in start
    logger.info("\u2705 Using Multi-Exchange as primary data source")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\u2705 Using Multi-Exchange as primary data source'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2053, in run_orchestrator
    await orchestrator.start()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 320, in start
    logger.info("\u2705 Using Multi-Exchange as primary data source")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\u2705 Using Multi-Exchange as primary data source'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2053, in run_orchestrator
    await orchestrator.start()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 323, in start
    await self._setup_fallback_subscriptions()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 475, in _setup_fallback_subscriptions
    logger.info("\u2705 Set up message bus subscriptions for Binance fallback data")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\u2705 Set up message bus subscriptions for Binance fallback data'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2053, in run_orchestrator
    await orchestrator.start()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 323, in start
    await self._setup_fallback_subscriptions()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 475, in _setup_fallback_subscriptions
    logger.info("\u2705 Set up message bus subscriptions for Binance fallback data")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\u2705 Set up message bus subscriptions for Binance fallback data'
Arguments: ()
2025-05-29 12:48:21,353 - strategy.smart_model_integrated - INFO - Loaded 60 historical funding rates for BTC-USDT
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2053, in run_orchestrator
    await orchestrator.start()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 386, in start
    logger.info("\u2705 Enhanced LLM Consumer started successfully")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\u2705 Enhanced LLM Consumer started successfully'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2053, in run_orchestrator
    await orchestrator.start()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 386, in start
    logger.info("\u2705 Enhanced LLM Consumer started successfully")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\u2705 Enhanced LLM Consumer started successfully'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f9e0' in position 79: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2053, in run_orchestrator
    await orchestrator.start()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 387, in start
    logger.info(f"\U0001f9e0 LLM Model: {self.config.get('model_path', 'Unknown')}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f9e0 LLM Model: Unknown'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f9e0' in position 67: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2242, in <module>
    asyncio.run(run_orchestrator(config))
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\base_events.py", line 2040, in _run_once
    handle._run()
  File "C:\Users\<USER>\miniconda3\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 2053, in run_orchestrator
    await orchestrator.start()
  File "C:\Users\<USER>\Documents\dev\smarty\orchestrator.py", line 387, in start
    logger.info(f"\U0001f9e0 LLM Model: {self.config.get('model_path', 'Unknown')}")
  File "C:\Users\<USER>\Documents\dev\smarty\utils\centralized_logging.py", line 88, in info
    self.logger.info(message, **kwargs)
Message: '\U0001f9e0 LLM Model: Unknown'
Arguments: ()
2025-05-29 12:48:21,358 - strategy.smart_model_integrated - INFO - Position manager started
2025-05-29 12:48:21,359 - strategy.smart_model_integrated - INFO - Starting event loop
2025-05-29 12:48:21,359 - strategy.smart_model_integrated - INFO - Started bus maintenance task
2025-05-29 12:48:21,359 - strategy.smart_model_integrated - ERROR - Error calculating features for BTC-USDT: 'RSIModel' object has no attribute '_calculate_rsi'
No current price available for BTC-USDT
2025-05-29 12:48:21,700 - strategy.smart_model_integrated - INFO - Starting account information update task
2025-05-29 12:48:21,700 - strategy.smart_model_integrated - ERROR - Error updating account information: REST client not initialized
2025-05-29 12:48:21,700 - strategy.smart_model_integrated - INFO - Starting health check task
2025-05-29 12:48:21,700 - strategy.smart_model_integrated - INFO - Starting position monitoring task
2025-05-29 12:48:21,700 - strategy.smart_model_integrated - INFO - Starting funding rate fetching task
2025-05-29 12:48:21,705 - strategy.smart_model_integrated - INFO - Starting open interest fetching task
2025-05-29 12:48:21,709 - strategy.smart_model_integrated - INFO - Bus maintenance scheduled every 24 hours, keeping messages for 7 days
