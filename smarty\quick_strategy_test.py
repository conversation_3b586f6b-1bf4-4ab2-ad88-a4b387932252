#!/usr/bin/env python3
"""
Quick test of the smart strategy to identify immediate issues.
"""

import asyncio
import logging
import yaml
from datetime import datetime, timedelta

from backtester.backtester import Backtester
from backtester.strategies import smart_model_integrated_strategy

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def quick_strategy_test():
    """Run a quick test of the smart strategy."""
    
    print("🧪 Quick Smart Strategy Test")
    print("=" * 40)
    
    # Load config
    try:
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        print("✅ Config loaded")
    except Exception as e:
        print(f"❌ Config load failed: {e}")
        return
    
    # Test parameters
    symbols = ['BTC-USDT']
    start_date = '2023-11-01'  # Short recent period
    end_date = '2023-12-31'
    
    print(f"📊 Testing: {', '.join(symbols)}")
    print(f"📅 Period: {start_date} to {end_date}")
    print()
    
    try:
        # Create backtester
        print("🔧 Initializing backtester...")
        backtester = Backtester(
            config=config,
            output_dir="results/quick_test"
        )
        
        await backtester.initialize()
        print("✅ Backtester initialized")
        
        # Load data
        print("📈 Loading historical data...")
        await backtester.load_data(symbols, start_date, end_date)
        print("✅ Data loaded")
        
        # Create signal generator
        async def signal_generator(timestamp, symbols_list):
            return await smart_model_integrated_strategy(timestamp, symbols_list)
        
        # Run backtest
        print("🏃 Running backtest...")
        success = await backtester.run_backtest(signal_generator=signal_generator)
        
        if success:
            print("✅ Backtest completed successfully!")
            
            # Print results
            metrics = backtester.metrics
            print("\n📊 RESULTS:")
            print("-" * 30)
            
            if isinstance(metrics, dict):
                print(f"Total Return: {metrics.get('total_return', 0):.2%}")
                print(f"Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.3f}")
                print(f"Max Drawdown: {metrics.get('max_drawdown', 0):.2%}")
                print(f"Win Rate: {metrics.get('win_rate', 0):.2%}")
                print(f"Total Trades: {metrics.get('total_trades', 0)}")
                print(f"Final Balance: ${metrics.get('final_balance', 0):.2f}")
            else:
                # Handle PerformanceMetrics object
                print(f"Total Return: {getattr(metrics, 'total_return', 0):.2%}")
                print(f"Sharpe Ratio: {getattr(metrics, 'sharpe_ratio', 0):.3f}")
                print(f"Max Drawdown: {getattr(metrics, 'max_drawdown', 0):.2%}")
                print(f"Win Rate: {getattr(metrics, 'win_rate', 0):.2%}")
                print(f"Total Trades: {getattr(metrics, 'total_trades', 0)}")
                print(f"Final Balance: ${getattr(metrics, 'final_balance', 0):.2f}")
            
            # Quick analysis
            print("\n🔍 QUICK ANALYSIS:")
            print("-" * 30)
            
            total_return = metrics.get('total_return', 0) if isinstance(metrics, dict) else getattr(metrics, 'total_return', 0)
            sharpe_ratio = metrics.get('sharpe_ratio', 0) if isinstance(metrics, dict) else getattr(metrics, 'sharpe_ratio', 0)
            win_rate = metrics.get('win_rate', 0) if isinstance(metrics, dict) else getattr(metrics, 'win_rate', 0)
            total_trades = metrics.get('total_trades', 0) if isinstance(metrics, dict) else getattr(metrics, 'total_trades', 0)
            
            if total_return < -0.05:
                print("🚨 Poor returns detected (< -5%)")
            elif total_return > 0.1:
                print("🎉 Good returns (> 10%)")
            else:
                print("📊 Moderate returns")
            
            if sharpe_ratio < 0:
                print("🚨 Negative Sharpe ratio - poor risk-adjusted returns")
            elif sharpe_ratio > 1:
                print("🎉 Good Sharpe ratio (> 1.0)")
            else:
                print("📊 Moderate Sharpe ratio")
            
            if win_rate < 0.4:
                print("🚨 Low win rate (< 40%)")
            elif win_rate > 0.6:
                print("🎉 Good win rate (> 60%)")
            else:
                print("📊 Moderate win rate")
            
            if total_trades == 0:
                print("🚨 No trades executed - check signal generation")
            elif total_trades < 5:
                print("⚠️ Very few trades - may need more signals")
            else:
                print(f"✅ {total_trades} trades executed")
            
            # Check signals
            signals = backtester.signals
            if signals:
                buy_signals = sum(1 for s in signals if s.get('action') == 'BUY')
                sell_signals = sum(1 for s in signals if s.get('action') == 'SELL')
                print(f"\n📡 SIGNALS: {len(signals)} total ({buy_signals} buy, {sell_signals} sell)")
            else:
                print("\n🚨 No signals generated!")
            
            # Check trades
            trades = backtester.trades
            if trades:
                profitable = sum(1 for t in trades if t.get('pnl', 0) > 0)
                print(f"💰 TRADES: {profitable}/{len(trades)} profitable")
            
        else:
            print("❌ Backtest failed!")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"Quick test error: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("🎯 RECOMMENDATIONS:")
    
    # Generate quick recommendations
    if total_return < -0.05:
        print("1. 🔧 Review signal generation logic")
        print("2. 📊 Check model data availability")
        print("3. ⚖️ Implement better risk management")
    
    if sharpe_ratio < 0:
        print("4. 📉 Reduce position volatility")
        print("5. 🎯 Improve signal quality/timing")
    
    if total_trades == 0:
        print("6. 🚨 URGENT: Fix signal generation - no trades!")
    elif total_trades < 5:
        print("6. 📈 Consider lowering signal thresholds")
    
    print("\n💡 Run full analysis with: python run_enhanced_backtest.py")
    
    return True


async def test_model_availability():
    """Test if models are providing data."""
    print("\n🤖 Testing Model Availability...")
    
    try:
        from core.feature_store import feature_store
        
        symbol = 'BTC-USDT'
        model_keys = [
            'vwap_deviation_prediction',
            'volatility_regime_prediction', 
            'funding_momentum_prediction',
            'oi_momentum_prediction',
            'rsi_model_prediction',
            'meta_ensemble_prediction'
        ]
        
        print(f"Checking models for {symbol}:")
        
        available_models = 0
        for model_key in model_keys:
            try:
                data = await feature_store.get(symbol, model_key)
                if data is not None:
                    print(f"  ✅ {model_key}")
                    available_models += 1
                else:
                    print(f"  ❌ {model_key} (no data)")
            except Exception as e:
                print(f"  ❌ {model_key} (error: {e})")
        
        print(f"\n📊 {available_models}/{len(model_keys)} models available")
        
        if available_models == 0:
            print("🚨 No models available! This will cause poor performance.")
            print("💡 Start the orchestrator to populate model data.")
        elif available_models < len(model_keys) // 2:
            print("⚠️ Many models unavailable. Performance may be poor.")
        else:
            print("✅ Good model availability.")
            
    except Exception as e:
        print(f"❌ Model availability test failed: {e}")


async def main():
    """Main function."""
    print("🚀 Smart Strategy Quick Test")
    print("This will run a fast test to identify immediate issues.\n")
    
    # Test model availability first
    await test_model_availability()
    
    # Run strategy test
    success = await quick_strategy_test()
    
    if success:
        print("\n✅ Quick test completed successfully!")
    else:
        print("\n❌ Quick test failed. Check the logs for details.")


if __name__ == "__main__":
    asyncio.run(main())
