#!/usr/bin/env python3
"""
Test Script for Smart Model Integrated Strategy
Tests the full orchestrator.py system with LLM integration.
"""

import asyncio
import logging
import time
import sqlite3
import json
import subprocess
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SmartModelIntegratedTester:
    """Test the Smart Model Integrated strategy."""
    
    def __init__(self):
        self.strategy_name = "Smart Model Integrated"
        self.command = "python orchestrator.py"
        self.process = None
        self.test_duration = 60  # Test for 60 seconds
        self.db_path = "data/bus.db"
        
    def test_database_connection(self) -> bool:
        """Test database connectivity."""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) as count FROM messages")
            count = cursor.fetchone()['count']
            conn.close()
            logger.info(f"✅ Database connected: {count} messages")
            return True
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return False
    
    def start_strategy(self) -> bool:
        """Start the Smart Model Integrated strategy."""
        try:
            logger.info(f"🚀 Starting {self.strategy_name}...")
            self.process = subprocess.Popen(
                self.command.split(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # Wait a moment for startup
            time.sleep(3)
            
            if self.process.poll() is None:
                logger.info(f"✅ Strategy started successfully (PID: {self.process.pid})")
                return True
            else:
                stdout, stderr = self.process.communicate()
                logger.error(f"❌ Strategy failed to start: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to start strategy: {e}")
            return False
    
    def check_process_running(self) -> bool:
        """Check if the strategy process is still running."""
        if self.process and self.process.poll() is None:
            return True
        return False
    
    def check_data_flow(self) -> Dict[str, Any]:
        """Check if data is flowing to the database."""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Check recent messages (last 30 seconds)
            recent_threshold = time.time() - 30
            cursor.execute(
                "SELECT COUNT(*) as count FROM messages WHERE ts > ?",
                (recent_threshold,)
            )
            recent_count = cursor.fetchone()['count']
            
            # Check for specific signal types
            cursor.execute(
                "SELECT COUNT(*) as count FROM messages WHERE stream LIKE 'signals.%' AND ts > ?",
                (recent_threshold,)
            )
            signal_count = cursor.fetchone()['count']
            
            # Check for market data
            cursor.execute(
                "SELECT COUNT(*) as count FROM messages WHERE stream LIKE 'market.%' AND ts > ?",
                (recent_threshold,)
            )
            market_count = cursor.fetchone()['count']
            
            # Check for LLM responses
            cursor.execute(
                "SELECT COUNT(*) as count FROM messages WHERE stream LIKE 'llm.%' AND ts > ?",
                (recent_threshold,)
            )
            llm_count = cursor.fetchone()['count']
            
            conn.close()
            
            return {
                "recent_messages": recent_count,
                "signal_messages": signal_count,
                "market_messages": market_count,
                "llm_messages": llm_count,
                "data_flowing": recent_count > 0
            }
            
        except Exception as e:
            logger.error(f"❌ Error checking data flow: {e}")
            return {"data_flowing": False, "error": str(e)}
    
    def check_strategy_components(self) -> Dict[str, bool]:
        """Check if strategy components are working."""
        components = {
            "orchestrator": False,
            "llm_consumer": False,
            "feature_store": False,
            "model_predictions": False,
            "signal_generation": False
        }
        
        try:
            # Check for orchestrator process
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] in ['python.exe', 'python']:
                        cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                        if 'orchestrator.py' in cmdline:
                            components["orchestrator"] = True
                            break
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Check database for component activity
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            recent_threshold = time.time() - 60
            
            # Check for LLM activity
            cursor.execute(
                "SELECT COUNT(*) as count FROM messages WHERE stream LIKE '%llm%' AND ts > ?",
                (recent_threshold,)
            )
            if cursor.fetchone()['count'] > 0:
                components["llm_consumer"] = True
            
            # Check for feature store activity
            cursor.execute(
                "SELECT COUNT(*) as count FROM messages WHERE stream LIKE '%feature%' AND ts > ?",
                (recent_threshold,)
            )
            if cursor.fetchone()['count'] > 0:
                components["feature_store"] = True
            
            # Check for model predictions
            cursor.execute(
                "SELECT COUNT(*) as count FROM messages WHERE stream LIKE '%model%' AND ts > ?",
                (recent_threshold,)
            )
            if cursor.fetchone()['count'] > 0:
                components["model_predictions"] = True
            
            # Check for signal generation
            cursor.execute(
                "SELECT COUNT(*) as count FROM messages WHERE stream LIKE 'signals.%' AND ts > ?",
                (recent_threshold,)
            )
            if cursor.fetchone()['count'] > 0:
                components["signal_generation"] = True
            
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ Error checking components: {e}")
        
        return components
    
    def stop_strategy(self) -> bool:
        """Stop the strategy process."""
        try:
            if self.process:
                logger.info(f"🛑 Stopping {self.strategy_name}...")
                self.process.terminate()
                
                # Wait for graceful shutdown
                try:
                    self.process.wait(timeout=10)
                    logger.info("✅ Strategy stopped gracefully")
                except subprocess.TimeoutExpired:
                    logger.warning("⚠️ Forcing strategy termination...")
                    self.process.kill()
                    self.process.wait()
                    logger.info("✅ Strategy force-stopped")
                
                return True
            else:
                logger.warning("⚠️ No process to stop")
                return True
                
        except Exception as e:
            logger.error(f"❌ Error stopping strategy: {e}")
            return False
    
    async def run_test(self) -> Dict[str, Any]:
        """Run the complete test suite."""
        logger.info(f"🎯 TESTING {self.strategy_name.upper()}")
        logger.info("=" * 60)
        
        results = {
            "strategy_name": self.strategy_name,
            "test_start_time": datetime.now().isoformat(),
            "database_connection": False,
            "strategy_startup": False,
            "process_running": False,
            "data_flow": {},
            "components": {},
            "strategy_shutdown": False,
            "overall_success": False,
            "test_duration": self.test_duration
        }
        
        # Test 1: Database Connection
        logger.info("📊 Testing database connection...")
        results["database_connection"] = self.test_database_connection()
        
        if not results["database_connection"]:
            logger.error("❌ Database test failed - aborting")
            return results
        
        # Test 2: Strategy Startup
        logger.info("🚀 Testing strategy startup...")
        results["strategy_startup"] = self.start_strategy()
        
        if not results["strategy_startup"]:
            logger.error("❌ Strategy startup failed - aborting")
            return results
        
        # Test 3: Process Running Check
        logger.info("🔍 Checking process status...")
        await asyncio.sleep(5)  # Wait for initialization
        results["process_running"] = self.check_process_running()
        
        # Test 4: Data Flow Monitoring
        logger.info(f"📡 Monitoring data flow for {self.test_duration} seconds...")
        
        for i in range(self.test_duration // 10):
            await asyncio.sleep(10)
            data_flow = self.check_data_flow()
            results["data_flow"] = data_flow
            
            logger.info(f"📈 Data flow check {i+1}: "
                       f"Recent={data_flow.get('recent_messages', 0)}, "
                       f"Signals={data_flow.get('signal_messages', 0)}, "
                       f"Market={data_flow.get('market_messages', 0)}, "
                       f"LLM={data_flow.get('llm_messages', 0)}")
            
            if not self.check_process_running():
                logger.error("❌ Process stopped unexpectedly")
                break
        
        # Test 5: Component Check
        logger.info("🔧 Checking strategy components...")
        results["components"] = self.check_strategy_components()
        
        # Test 6: Strategy Shutdown
        logger.info("🛑 Testing strategy shutdown...")
        results["strategy_shutdown"] = self.stop_strategy()
        
        # Overall Assessment
        results["overall_success"] = (
            results["database_connection"] and
            results["strategy_startup"] and
            results["process_running"] and
            results["data_flow"].get("data_flowing", False) and
            results["strategy_shutdown"]
        )
        
        results["test_end_time"] = datetime.now().isoformat()
        
        # Print Results
        logger.info("\n📊 TEST RESULTS SUMMARY")
        logger.info("=" * 40)
        logger.info(f"Strategy: {self.strategy_name}")
        logger.info(f"Database Connection: {'✅' if results['database_connection'] else '❌'}")
        logger.info(f"Strategy Startup: {'✅' if results['strategy_startup'] else '❌'}")
        logger.info(f"Process Running: {'✅' if results['process_running'] else '❌'}")
        logger.info(f"Data Flowing: {'✅' if results['data_flow'].get('data_flowing') else '❌'}")
        logger.info(f"Strategy Shutdown: {'✅' if results['strategy_shutdown'] else '❌'}")
        
        logger.info("\nComponent Status:")
        for component, status in results["components"].items():
            logger.info(f"  {component}: {'✅' if status else '❌'}")
        
        logger.info(f"\nOverall Success: {'✅ PASS' if results['overall_success'] else '❌ FAIL'}")
        
        return results

async def main():
    """Main test execution."""
    tester = SmartModelIntegratedTester()
    results = await tester.run_test()
    
    # Save results to file
    with open(f"test_results_{tester.strategy_name.lower().replace(' ', '_')}.json", "w") as f:
        json.dump(results, f, indent=2)
    
    return results["overall_success"]

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
