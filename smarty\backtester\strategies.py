"""
Trading strategies for backtesting.

This module provides a collection of trading strategies that can be used
with the backtesting framework.
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple

from core.events import Signal, Side
from core.feature_store import feature_store

logger = logging.getLogger(__name__)


async def simple_moving_average_crossover(
    timestamp: datetime,
    symbols: List[str],
    fast_period: int = 10,
    slow_period: int = 30,
    signal_source: str = "sma_crossover"
) -> List[Signal]:
    """
    Simple Moving Average Crossover strategy.

    This strategy generates buy signals when the fast moving average crosses
    above the slow moving average, and sell signals when the fast moving average
    crosses below the slow moving average.

    Args:
        timestamp: Current timestamp
        symbols: List of symbols to generate signals for
        fast_period: Fast moving average period
        slow_period: Slow moving average period
        signal_source: Source identifier for the signal

    Returns:
        List of signals
    """
    signals = []

    for symbol in symbols:
        try:
            # Get historical close prices
            close_prices = await feature_store.get_time_series(symbol, "close_prices", slow_period + 10)

            if not close_prices or len(close_prices) < slow_period:
                continue

            # Extract price values
            prices = []
            timestamps = []
            for price_data in close_prices:
                if isinstance(price_data, (list, tuple)) and len(price_data) >= 2:
                    # If it's a tuple/list of (timestamp, value)
                    ts = price_data[0]
                    if isinstance(ts, (pd.Timestamp, datetime)):
                        timestamps.append(ts)
                    prices.append(float(price_data[1]))
                elif isinstance(price_data, (int, float, str)):
                    # If it's just the value
                    prices.append(float(price_data))

            if len(prices) < slow_period:
                continue

            # Calculate moving averages
            prices_array = np.array(prices)
            fast_ma = np.mean(prices_array[-fast_period:])
            slow_ma = np.mean(prices_array[-slow_period:])

            # Calculate previous moving averages
            prev_fast_ma = np.mean(prices_array[-fast_period-1:-1])
            prev_slow_ma = np.mean(prices_array[-slow_period-1:-1])

            # Check for crossover
            current_price = prices_array[-1]

            # Buy signal: fast MA crosses above slow MA
            if prev_fast_ma <= prev_slow_ma and fast_ma > slow_ma:
                # Get current price
                current_price = prices_array[-1]

                signal = Signal(
                    symbol=symbol,
                    timestamp=timestamp,
                    action=Side.BUY,
                    score=0.7,  # Confidence score
                    source=signal_source,
                    rationale=f"Fast MA ({fast_period}) crossed above Slow MA ({slow_period})",
                    metadata={"price": current_price}
                )
                signals.append(signal)
                logger.info(f"Generated BUY signal for {symbol} at {timestamp}")

            # Sell signal: fast MA crosses below slow MA
            elif prev_fast_ma >= prev_slow_ma and fast_ma < slow_ma:
                # Get current price
                current_price = prices_array[-1]

                signal = Signal(
                    symbol=symbol,
                    timestamp=timestamp,
                    action=Side.SELL,
                    score=0.7,  # Confidence score
                    source=signal_source,
                    rationale=f"Fast MA ({fast_period}) crossed below Slow MA ({slow_period})",
                    metadata={"price": current_price}
                )
                signals.append(signal)
                logger.info(f"Generated SELL signal for {symbol} at {timestamp}")

        except Exception as e:
            logger.error(f"Error generating signals for {symbol}: {e}")

    return signals


async def bollinger_bands_strategy(
    timestamp: datetime,
    symbols: List[str],
    period: int = 20,
    std_dev: float = 2.0,
    signal_source: str = "bollinger_bands"
) -> List[Signal]:
    """
    Bollinger Bands strategy.

    This strategy generates buy signals when the price touches the lower band
    and sell signals when the price touches the upper band.

    Args:
        timestamp: Current timestamp
        symbols: List of symbols to generate signals for
        period: Bollinger Bands period
        std_dev: Number of standard deviations for the bands
        signal_source: Source identifier for the signal

    Returns:
        List of signals
    """
    signals = []

    for symbol in symbols:
        try:
            # Get historical close prices
            close_prices = await feature_store.get_time_series(symbol, "close_prices", period + 10)

            if not close_prices or len(close_prices) < period:
                continue

            # Extract price values
            prices = []
            timestamps = []
            for price_data in close_prices:
                if isinstance(price_data, (list, tuple)) and len(price_data) >= 2:
                    # If it's a tuple/list of (timestamp, value)
                    ts = price_data[0]
                    if isinstance(ts, (pd.Timestamp, datetime)):
                        timestamps.append(ts)
                    prices.append(float(price_data[1]))
                elif isinstance(price_data, (int, float, str)):
                    # If it's just the value
                    prices.append(float(price_data))

            if len(prices) < period:
                continue

            # Calculate Bollinger Bands
            prices_array = np.array(prices[-period:])
            middle_band = np.mean(prices_array)
            std = np.std(prices_array)
            upper_band = middle_band + (std_dev * std)
            lower_band = middle_band - (std_dev * std)

            # Get current price
            current_price = prices[-1]
            prev_price = prices[-2]

            # Buy signal: price crosses above lower band from below
            if prev_price <= lower_band and current_price > lower_band:
                signal = Signal(
                    symbol=symbol,
                    timestamp=timestamp,
                    action=Side.BUY,
                    score=0.6,  # Confidence score
                    source=signal_source,
                    rationale=f"Price crossed above lower Bollinger Band",
                    metadata={"price": current_price}
                )
                signals.append(signal)
                logger.info(f"Generated BUY signal for {symbol} at {timestamp}")

            # Sell signal: price crosses below upper band from above
            elif prev_price >= upper_band and current_price < upper_band:
                signal = Signal(
                    symbol=symbol,
                    timestamp=timestamp,
                    action=Side.SELL,
                    score=0.6,  # Confidence score
                    source=signal_source,
                    rationale=f"Price crossed below upper Bollinger Band",
                    metadata={"price": current_price}
                )
                signals.append(signal)
                logger.info(f"Generated SELL signal for {symbol} at {timestamp}")

        except Exception as e:
            logger.error(f"Error generating signals for {symbol}: {e}")

    return signals


async def rsi_strategy(
    timestamp: datetime,
    symbols: List[str],
    period: int = 14,
    oversold: float = 30.0,
    overbought: float = 70.0,
    signal_source: str = "rsi"
) -> List[Signal]:
    """
    Relative Strength Index (RSI) strategy.

    This strategy generates buy signals when the RSI crosses above the oversold level
    and sell signals when the RSI crosses below the overbought level.

    Args:
        timestamp: Current timestamp
        symbols: List of symbols to generate signals for
        period: RSI period
        oversold: Oversold threshold
        overbought: Overbought threshold
        signal_source: Source identifier for the signal

    Returns:
        List of signals
    """
    signals = []

    for symbol in symbols:
        try:
            # Get historical close prices
            close_prices = await feature_store.get_time_series(symbol, "close_prices", period + 10)

            if not close_prices or len(close_prices) < period + 1:
                continue

            # Extract price values
            prices = []
            timestamps = []
            for price_data in close_prices:
                if isinstance(price_data, (list, tuple)) and len(price_data) >= 2:
                    # If it's a tuple/list of (timestamp, value)
                    ts = price_data[0]
                    if isinstance(ts, (pd.Timestamp, datetime)):
                        timestamps.append(ts)
                    prices.append(float(price_data[1]))
                elif isinstance(price_data, (int, float, str)):
                    # If it's just the value
                    prices.append(float(price_data))

            if len(prices) < period + 1:
                continue

            # Calculate RSI using a simpler method
            prices_array = np.array(prices)

            # Calculate gains and losses
            deltas = np.diff(prices_array)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)

            # Calculate average gains and losses
            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])

            # Calculate RS and RSI
            rs = avg_gain / avg_loss if avg_loss != 0 else float('inf')
            rsi = 100 - (100 / (1 + rs))

            # Calculate previous RSI
            if len(deltas) > 1:
                prev_gains = np.where(deltas[:-1] > 0, deltas[:-1], 0)
                prev_losses = np.where(deltas[:-1] < 0, -deltas[:-1], 0)

                prev_avg_gain = np.mean(prev_gains[-period:])
                prev_avg_loss = np.mean(prev_losses[-period:])

                prev_rs = prev_avg_gain / prev_avg_loss if prev_avg_loss != 0 else float('inf')
                prev_rsi = 100 - (100 / (1 + prev_rs))
            else:
                # If not enough data for previous RSI, use current RSI
                prev_rsi = rsi

            # Buy signal: RSI crosses above oversold level
            if prev_rsi <= oversold and rsi > oversold:
                # Get current price
                current_price = prices_array[-1]

                signal = Signal(
                    symbol=symbol,
                    timestamp=timestamp,
                    action=Side.BUY,
                    score=0.65,  # Confidence score
                    source=signal_source,
                    rationale=f"RSI crossed above oversold level ({oversold})",
                    metadata={"price": current_price}
                )
                signals.append(signal)
                logger.info(f"Generated BUY signal for {symbol} at {timestamp}")

            # Sell signal: RSI crosses below overbought level
            elif prev_rsi >= overbought and rsi < overbought:
                # Get current price
                current_price = prices_array[-1]

                signal = Signal(
                    symbol=symbol,
                    timestamp=timestamp,
                    action=Side.SELL,
                    score=0.65,  # Confidence score
                    source=signal_source,
                    rationale=f"RSI crossed below overbought level ({overbought})",
                    metadata={"price": current_price}
                )
                signals.append(signal)
                logger.info(f"Generated SELL signal for {symbol} at {timestamp}")

        except Exception as e:
            logger.error(f"Error generating signals for {symbol}: {e}")

    return signals


async def enhanced_multi_signal_strategy(
    timestamp: datetime,
    symbols: List[str],
    sma_fast: int = 10,
    sma_slow: int = 30,
    rsi_period: int = 14,
    rsi_oversold: float = 30.0,
    rsi_overbought: float = 70.0,
    bb_period: int = 20,
    bb_std: float = 2.0,
    signal_source: str = "multi_signal"
) -> List[Signal]:
    """
    Enhanced multi-signal strategy combining SMA, RSI, and Bollinger Bands.

    This strategy combines multiple technical indicators to generate more robust signals:
    - SMA crossover for trend direction
    - RSI for momentum confirmation
    - Bollinger Bands for volatility-based entry/exit

    Args:
        timestamp: Current timestamp
        symbols: List of symbols to generate signals for
        sma_fast: Fast SMA period
        sma_slow: Slow SMA period
        rsi_period: RSI period
        rsi_oversold: RSI oversold threshold
        rsi_overbought: RSI overbought threshold
        bb_period: Bollinger Bands period
        bb_std: Bollinger Bands standard deviation
        signal_source: Source identifier for the signal

    Returns:
        List of signals
    """
    signals = []

    for symbol in symbols:
        try:
            # Get price data
            prices_data = await feature_store.get_time_series(symbol, "close_prices", max(sma_slow + 5, bb_period + 5))

            if not prices_data or len(prices_data) < max(sma_slow + 1, bb_period + 1, rsi_period + 1):
                logger.warning(f"Not enough price data for {symbol}: {len(prices_data) if prices_data else 0} points")
                continue

            # Extract price values from tuples (timestamp, value)
            prices = []
            for price_data in prices_data:
                if isinstance(price_data, (list, tuple)) and len(price_data) >= 2:
                    # If it's a tuple/list of (timestamp, value)
                    prices.append(float(price_data[1]))
                elif isinstance(price_data, (int, float, str)):
                    # If it's just the value
                    prices.append(float(price_data))

            prices_array = np.array(prices)
            current_price = prices_array[-1]

            # Calculate SMA signals
            fast_ma = np.mean(prices_array[-sma_fast:])
            slow_ma = np.mean(prices_array[-sma_slow:])
            prev_fast_ma = np.mean(prices_array[-sma_fast-1:-1])
            prev_slow_ma = np.mean(prices_array[-sma_slow-1:-1])

            # SMA trend signal
            sma_bullish = fast_ma > slow_ma
            sma_crossover_up = prev_fast_ma <= prev_slow_ma and fast_ma > slow_ma
            sma_crossover_down = prev_fast_ma >= prev_slow_ma and fast_ma < slow_ma

            # Calculate RSI
            deltas = np.diff(prices_array)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            avg_gain = np.mean(gains[-rsi_period:])
            avg_loss = np.mean(losses[-rsi_period:])
            rs = avg_gain / avg_loss if avg_loss != 0 else float('inf')
            rsi = 100 - (100 / (1 + rs))

            # RSI signals
            rsi_oversold_signal = rsi < rsi_oversold
            rsi_overbought_signal = rsi > rsi_overbought
            rsi_neutral = rsi_oversold <= rsi <= rsi_overbought

            # Calculate Bollinger Bands
            bb_prices = prices_array[-bb_period:]
            bb_middle = np.mean(bb_prices)
            bb_std_dev = np.std(bb_prices)
            bb_upper = bb_middle + (bb_std * bb_std_dev)
            bb_lower = bb_middle - (bb_std * bb_std_dev)

            # Bollinger Bands signals
            bb_oversold = current_price <= bb_lower
            bb_overbought = current_price >= bb_upper
            bb_middle_cross_up = prices_array[-2] <= bb_middle and current_price > bb_middle
            bb_middle_cross_down = prices_array[-2] >= bb_middle and current_price < bb_middle

            # Combine signals for BUY
            buy_conditions = [
                sma_crossover_up,  # Strong trend signal
                sma_bullish and rsi_oversold_signal,  # Trend + oversold
                sma_bullish and bb_oversold,  # Trend + BB oversold
                bb_middle_cross_up and rsi_neutral,  # BB momentum + neutral RSI
            ]

            # Combine signals for SELL
            sell_conditions = [
                sma_crossover_down,  # Strong trend signal
                not sma_bullish and rsi_overbought_signal,  # Downtrend + overbought
                not sma_bullish and bb_overbought,  # Downtrend + BB overbought
                bb_middle_cross_down and rsi_neutral,  # BB momentum + neutral RSI
            ]

            # Calculate signal strength based on number of confirming indicators
            buy_score = sum(buy_conditions)
            sell_score = sum(sell_conditions)

            # Generate BUY signal
            if buy_score >= 1:  # At least one buy condition
                confidence = 0.5 + (buy_score * 0.15)  # Base 0.5, +0.15 per condition
                confidence = min(confidence, 0.95)  # Cap at 0.95

                # Create rationale
                active_conditions = []
                if sma_crossover_up:
                    active_conditions.append("SMA crossover up")
                if sma_bullish and rsi_oversold_signal:
                    active_conditions.append("bullish trend + RSI oversold")
                if sma_bullish and bb_oversold:
                    active_conditions.append("bullish trend + BB oversold")
                if bb_middle_cross_up and rsi_neutral:
                    active_conditions.append("BB middle cross up + neutral RSI")

                rationale = f"Multi-signal BUY: {', '.join(active_conditions)}"

                signal = Signal(
                    symbol=symbol,
                    timestamp=timestamp,
                    action=Side.BUY,
                    score=confidence,
                    source=signal_source,
                    rationale=rationale,
                    metadata={
                        "price": current_price,
                        "sma_fast": fast_ma,
                        "sma_slow": slow_ma,
                        "rsi": rsi,
                        "bb_upper": bb_upper,
                        "bb_lower": bb_lower,
                        "signal_count": buy_score
                    }
                )
                signals.append(signal)
                logger.info(f"Generated BUY signal for {symbol} at {timestamp} (score: {confidence:.2f})")

            # Generate SELL signal
            elif sell_score >= 1:  # At least one sell condition
                confidence = 0.5 + (sell_score * 0.15)  # Base 0.5, +0.15 per condition
                confidence = min(confidence, 0.95)  # Cap at 0.95

                # Create rationale
                active_conditions = []
                if sma_crossover_down:
                    active_conditions.append("SMA crossover down")
                if not sma_bullish and rsi_overbought_signal:
                    active_conditions.append("bearish trend + RSI overbought")
                if not sma_bullish and bb_overbought:
                    active_conditions.append("bearish trend + BB overbought")
                if bb_middle_cross_down and rsi_neutral:
                    active_conditions.append("BB middle cross down + neutral RSI")

                rationale = f"Multi-signal SELL: {', '.join(active_conditions)}"

                signal = Signal(
                    symbol=symbol,
                    timestamp=timestamp,
                    action=Side.SELL,
                    score=confidence,
                    source=signal_source,
                    rationale=rationale,
                    metadata={
                        "price": current_price,
                        "sma_fast": fast_ma,
                        "sma_slow": slow_ma,
                        "rsi": rsi,
                        "bb_upper": bb_upper,
                        "bb_lower": bb_lower,
                        "signal_count": sell_score
                    }
                )
                signals.append(signal)
                logger.info(f"Generated SELL signal for {symbol} at {timestamp} (score: {confidence:.2f})")

        except Exception as e:
            logger.error(f"Error generating multi-signal for {symbol}: {e}")

    return signals


async def model_ensemble_strategy(
    timestamp: datetime,
    symbols: List[str],
    signal_source: str = "model_ensemble"
) -> List[Signal]:
    """
    Model ensemble strategy that combines signals from multiple models.

    This strategy integrates signals from:
    - VWAP deviation model
    - RSI model
    - Volatility regime model
    - Funding momentum model
    - Open interest momentum model

    Args:
        timestamp: Current timestamp
        symbols: List of symbols to generate signals for
        signal_source: Source identifier for the signal

    Returns:
        List of signals
    """
    signals = []

    for symbol in symbols:
        try:
            # Collect model signals and weights
            model_signals = {}
            total_weight = 0.0

            # Get VWAP deviation signal
            vwap_signal = await feature_store.get(symbol, "vwap_signal")
            if vwap_signal:
                weight = 1.0
                if vwap_signal == "BUY":
                    model_signals["vwap"] = (1.0, weight)
                elif vwap_signal == "SELL":
                    model_signals["vwap"] = (-1.0, weight)
                else:
                    model_signals["vwap"] = (0.0, weight)
                total_weight += weight

            # Get RSI signal
            rsi_signal = await feature_store.get(symbol, "rsi_signal")
            if rsi_signal:
                weight = 1.2  # RSI gets slightly higher weight
                if rsi_signal == "BUY":
                    model_signals["rsi"] = (1.0, weight)
                elif rsi_signal == "SELL":
                    model_signals["rsi"] = (-1.0, weight)
                else:
                    model_signals["rsi"] = (0.0, weight)
                total_weight += weight

            # Get funding momentum signal
            funding_signal = await feature_store.get(symbol, "funding_signal")
            if funding_signal:
                weight = 0.8  # Funding gets lower weight
                if funding_signal == "BUY":
                    model_signals["funding"] = (1.0, weight)
                elif funding_signal == "SELL":
                    model_signals["funding"] = (-1.0, weight)
                else:
                    model_signals["funding"] = (0.0, weight)
                total_weight += weight

            # Get volatility regime signal
            volatility_signal = await feature_store.get(symbol, "volatility_signal")
            if volatility_signal:
                weight = 0.9
                if volatility_signal == "BUY":
                    model_signals["volatility"] = (1.0, weight)
                elif volatility_signal == "SELL":
                    model_signals["volatility"] = (-1.0, weight)
                else:
                    model_signals["volatility"] = (0.0, weight)
                total_weight += weight

            # Calculate weighted ensemble signal
            if total_weight > 0:
                weighted_sum = sum(signal * weight for signal, weight in model_signals.values())
                ensemble_score = weighted_sum / total_weight

                # Determine action based on ensemble score
                if ensemble_score > 0.3:  # Bullish threshold
                    action = Side.BUY
                    confidence = min(0.95, 0.5 + abs(ensemble_score) * 0.4)
                elif ensemble_score < -0.3:  # Bearish threshold
                    action = Side.SELL
                    confidence = min(0.95, 0.5 + abs(ensemble_score) * 0.4)
                else:
                    continue  # No clear signal

                # Get current price
                current_price = await feature_store.get(symbol, "close")
                if not current_price:
                    continue

                # Create rationale
                active_models = [name for name, (signal, _) in model_signals.items() if abs(signal) > 0.1]
                rationale = f"Ensemble {action.value}: {', '.join(active_models)} (score: {ensemble_score:.2f})"

                signal = Signal(
                    symbol=symbol,
                    timestamp=timestamp,
                    action=action,
                    score=confidence,
                    source=signal_source,
                    rationale=rationale,
                    metadata={
                        "price": current_price,
                        "ensemble_score": ensemble_score,
                        "model_signals": {name: signal for name, (signal, _) in model_signals.items()},
                        "total_weight": total_weight
                    }
                )
                signals.append(signal)
                logger.info(f"Generated {action.value} ensemble signal for {symbol} at {timestamp} (score: {confidence:.2f})")

        except Exception as e:
            logger.error(f"Error generating ensemble signal for {symbol}: {e}")

    return signals


async def smart_model_integrated_strategy(
    timestamp: datetime,
    symbols: List[str],
    signal_source: str = "smart_integrated"
) -> List[Signal]:
    """
    Smart Model-Integrated Strategy that combines technical analysis with ML models.

    This strategy integrates:
    - Technical indicators (SMA, RSI, Bollinger Bands)
    - VWAP deviation analysis
    - Volatility regime detection
    - Funding rate momentum
    - Open interest analysis
    - RSI model predictions
    - Ensemble model outputs

    The strategy uses a sophisticated scoring system that weights different
    signal sources based on their historical performance and current market conditions.

    Args:
        timestamp: Current timestamp
        symbols: List of symbols to generate signals for
        signal_source: Source identifier for the signal

    Returns:
        List of signals
    """
    signals = []

    for symbol in symbols:
        try:
            # Get current price
            current_price = await feature_store.get(symbol, "close")
            if not current_price:
                logger.warning(f"No current price available for {symbol}")
                continue

            # Initialize signal components
            signal_components = {}
            total_weight = 0.0

            # 1. Technical Analysis Component (Weight: 0.3)
            tech_signal = await _get_technical_signal(symbol, timestamp)
            if tech_signal:
                signal_components["technical"] = {
                    "signal": tech_signal,
                    "weight": 0.3,
                    "confidence": abs(tech_signal)
                }
                total_weight += 0.3

            # 2. VWAP Deviation Component (Weight: 0.2)
            vwap_signal = await _get_vwap_signal(symbol)
            if vwap_signal:
                signal_components["vwap"] = {
                    "signal": vwap_signal,
                    "weight": 0.2,
                    "confidence": abs(vwap_signal)
                }
                total_weight += 0.2

            # 3. RSI Model Component (Weight: 0.15)
            rsi_signal = await _get_rsi_model_signal(symbol)
            if rsi_signal:
                signal_components["rsi_model"] = {
                    "signal": rsi_signal,
                    "weight": 0.15,
                    "confidence": abs(rsi_signal)
                }
                total_weight += 0.15

            # 4. Volatility Regime Component (Weight: 0.1)
            vol_signal = await _get_volatility_signal(symbol)
            if vol_signal:
                signal_components["volatility"] = {
                    "signal": vol_signal,
                    "weight": 0.1,
                    "confidence": abs(vol_signal)
                }
                total_weight += 0.1

            # 5. Funding Momentum Component (Weight: 0.1)
            funding_signal = await _get_funding_signal(symbol)
            if funding_signal:
                signal_components["funding"] = {
                    "signal": funding_signal,
                    "weight": 0.1,
                    "confidence": abs(funding_signal)
                }
                total_weight += 0.1

            # 6. Open Interest Component (Weight: 0.1)
            oi_signal = await _get_open_interest_signal(symbol)
            if oi_signal:
                signal_components["open_interest"] = {
                    "signal": oi_signal,
                    "weight": 0.1,
                    "confidence": abs(oi_signal)
                }
                total_weight += 0.1

            # 7. Meta-Ensemble Component (Weight: 0.05)
            ensemble_signal = await _get_ensemble_signal(symbol)
            if ensemble_signal:
                signal_components["ensemble"] = {
                    "signal": ensemble_signal,
                    "weight": 0.05,
                    "confidence": abs(ensemble_signal)
                }
                total_weight += 0.05

            # Calculate weighted ensemble score
            if total_weight > 0:
                weighted_sum = sum(
                    comp["signal"] * comp["weight"]
                    for comp in signal_components.values()
                )
                ensemble_score = weighted_sum / total_weight

                # Apply confidence weighting
                confidence_weighted_score = ensemble_score * _calculate_confidence_multiplier(signal_components)

                # Determine action based on ensemble score with dynamic thresholds
                buy_threshold, sell_threshold = _get_dynamic_thresholds(symbol, signal_components)

                if confidence_weighted_score > buy_threshold:
                    action = Side.BUY
                    confidence = min(0.95, 0.5 + abs(confidence_weighted_score) * 0.4)
                elif confidence_weighted_score < sell_threshold:
                    action = Side.SELL
                    confidence = min(0.95, 0.5 + abs(confidence_weighted_score) * 0.4)
                else:
                    continue  # No clear signal

                # Create detailed rationale
                active_components = [
                    f"{name}({comp['signal']:.2f})"
                    for name, comp in signal_components.items()
                    if abs(comp['signal']) > 0.1
                ]
                rationale = f"Smart-Integrated {action.value}: {', '.join(active_components)} → {confidence_weighted_score:.2f}"

                signal = Signal(
                    symbol=symbol,
                    timestamp=timestamp,
                    action=action,
                    score=confidence,
                    source=signal_source,
                    rationale=rationale,
                    metadata={
                        "price": current_price,
                        "ensemble_score": ensemble_score,
                        "confidence_weighted_score": confidence_weighted_score,
                        "components": {name: comp["signal"] for name, comp in signal_components.items()},
                        "total_weight": total_weight,
                        "buy_threshold": buy_threshold,
                        "sell_threshold": sell_threshold
                    }
                )
                signals.append(signal)
                logger.info(f"Generated {action.value} smart-integrated signal for {symbol} at {timestamp} (score: {confidence:.2f})")

        except Exception as e:
            logger.error(f"Error generating smart-integrated signal for {symbol}: {e}")

    return signals


async def _get_technical_signal(symbol: str, timestamp: datetime) -> Optional[float]:
    """Get technical analysis signal combining SMA, RSI, and Bollinger Bands."""
    try:
        # Get price data
        prices_data = await feature_store.get_time_series(symbol, "close_prices", 50)
        if not prices_data or len(prices_data) < 30:
            return None

        # Extract price values
        prices = []
        for price_data in prices_data:
            if isinstance(price_data, (list, tuple)) and len(price_data) >= 2:
                prices.append(float(price_data[1]))
            elif isinstance(price_data, (int, float, str)):
                prices.append(float(price_data))

        prices_array = np.array(prices)
        current_price = prices_array[-1]

        # SMA signals
        fast_ma = np.mean(prices_array[-10:])
        slow_ma = np.mean(prices_array[-30:])
        sma_signal = 1.0 if fast_ma > slow_ma else -1.0

        # RSI signal
        deltas = np.diff(prices_array)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        avg_gain = np.mean(gains[-14:])
        avg_loss = np.mean(losses[-14:])
        rs = avg_gain / avg_loss if avg_loss != 0 else float('inf')
        rsi = 100 - (100 / (1 + rs))

        if rsi < 30:
            rsi_signal = 1.0  # Oversold -> Buy
        elif rsi > 70:
            rsi_signal = -1.0  # Overbought -> Sell
        else:
            rsi_signal = 0.0

        # Bollinger Bands signal
        bb_period = 20
        if len(prices_array) >= bb_period:
            bb_prices = prices_array[-bb_period:]
            bb_middle = np.mean(bb_prices)
            bb_std = np.std(bb_prices)
            bb_upper = bb_middle + (2.0 * bb_std)
            bb_lower = bb_middle - (2.0 * bb_std)

            if current_price <= bb_lower:
                bb_signal = 1.0  # Below lower band -> Buy
            elif current_price >= bb_upper:
                bb_signal = -1.0  # Above upper band -> Sell
            else:
                bb_signal = 0.0
        else:
            bb_signal = 0.0

        # Combine technical signals
        tech_signal = (sma_signal * 0.5) + (rsi_signal * 0.3) + (bb_signal * 0.2)
        return tech_signal

    except Exception as e:
        logger.error(f"Error calculating technical signal for {symbol}: {e}")
        return None


async def _get_vwap_signal(symbol: str) -> Optional[float]:
    """Get VWAP deviation signal."""
    try:
        vwap_prediction = await feature_store.get(symbol, "vwap_deviation_prediction")
        if not vwap_prediction:
            return None

        z_score = vwap_prediction.get("z_score", 0.0)
        signal_enum = vwap_prediction.get("signal", "AT_VWAP")

        # Convert VWAP signal to numeric
        if signal_enum == "BELOW_VWAP":
            return min(1.0, abs(z_score) / 2.0)  # Buy signal
        elif signal_enum == "ABOVE_VWAP":
            return max(-1.0, -abs(z_score) / 2.0)  # Sell signal
        else:
            return 0.0

    except Exception as e:
        logger.error(f"Error getting VWAP signal for {symbol}: {e}")
        return None


async def _get_rsi_model_signal(symbol: str) -> Optional[float]:
    """Get RSI model signal."""
    try:
        rsi_prediction = await feature_store.get(symbol, "rsi_prediction")
        if not rsi_prediction:
            return None

        return rsi_prediction.get("signal_strength", 0.0)

    except Exception as e:
        logger.error(f"Error getting RSI model signal for {symbol}: {e}")
        return None


async def _get_volatility_signal(symbol: str) -> Optional[float]:
    """Get volatility regime signal."""
    try:
        vol_prediction = await feature_store.get(symbol, "garch_volatility_prediction")
        if not vol_prediction:
            return None

        regime = vol_prediction.get("regime", "NORMAL")
        vol_z = vol_prediction.get("volatility_z_score", 0.0)

        # High volatility -> reduce position sizes (negative signal)
        # Low volatility -> increase position sizes (positive signal)
        if regime == "HIGH_VOLATILITY":
            return max(-0.5, -abs(vol_z) / 4.0)
        elif regime == "LOW_VOLATILITY":
            return min(0.5, abs(vol_z) / 4.0)
        else:
            return 0.0

    except Exception as e:
        logger.error(f"Error getting volatility signal for {symbol}: {e}")
        return None


async def _get_funding_signal(symbol: str) -> Optional[float]:
    """Get funding momentum signal."""
    try:
        funding_prediction = await feature_store.get(symbol, "funding_momentum_prediction")
        if not funding_prediction:
            return None

        action = funding_prediction.get("action", "HOLD")
        confidence = funding_prediction.get("confidence", 0.0)

        if action == "BUY":
            return confidence
        elif action == "SELL":
            return -confidence
        else:
            return 0.0

    except Exception as e:
        logger.error(f"Error getting funding signal for {symbol}: {e}")
        return None


async def _get_open_interest_signal(symbol: str) -> Optional[float]:
    """Get open interest momentum signal."""
    try:
        oi_prediction = await feature_store.get(symbol, "open_interest_momentum_prediction")
        if not oi_prediction:
            return None

        action = oi_prediction.get("action", "HOLD")
        confidence = oi_prediction.get("confidence", 0.0)

        if action == "BUY":
            return confidence
        elif action == "SELL":
            return -confidence
        else:
            return 0.0

    except Exception as e:
        logger.error(f"Error getting open interest signal for {symbol}: {e}")
        return None


async def _get_ensemble_signal(symbol: str) -> Optional[float]:
    """Get meta-ensemble signal."""
    try:
        ensemble_prediction = await feature_store.get(symbol, "meta_ensemble_prediction")
        if not ensemble_prediction:
            return None

        action = ensemble_prediction.get("action", "HOLD")
        confidence_lower = ensemble_prediction.get("confidence_lower", 0.0)
        confidence_upper = ensemble_prediction.get("confidence_upper", 0.0)

        # Use average confidence
        avg_confidence = (confidence_lower + confidence_upper) / 2.0

        if action == "BUY":
            return avg_confidence
        elif action == "SELL":
            return -avg_confidence
        else:
            return 0.0

    except Exception as e:
        logger.error(f"Error getting ensemble signal for {symbol}: {e}")
        return None


def _calculate_confidence_multiplier(signal_components: Dict[str, Dict[str, Any]]) -> float:
    """Calculate confidence multiplier based on signal agreement."""
    if not signal_components:
        return 1.0

    # Count positive and negative signals
    positive_signals = sum(1 for comp in signal_components.values() if comp["signal"] > 0.1)
    negative_signals = sum(1 for comp in signal_components.values() if comp["signal"] < -0.1)
    total_signals = len(signal_components)

    # Calculate agreement ratio
    max_agreement = max(positive_signals, negative_signals)
    agreement_ratio = max_agreement / total_signals if total_signals > 0 else 0.0

    # Boost confidence when signals agree
    if agreement_ratio > 0.7:
        return 1.3  # High agreement
    elif agreement_ratio > 0.5:
        return 1.1  # Moderate agreement
    else:
        return 0.8  # Low agreement


def _get_dynamic_thresholds(symbol: str, signal_components: Dict[str, Dict[str, Any]]) -> Tuple[float, float]:
    """Get dynamic buy/sell thresholds based on signal strength and market conditions."""
    # Base thresholds
    base_buy_threshold = 0.3
    base_sell_threshold = -0.3

    # Adjust based on signal strength
    if signal_components:
        avg_confidence = np.mean([comp["confidence"] for comp in signal_components.values()])

        # Lower thresholds when confidence is high
        if avg_confidence > 0.7:
            buy_threshold = base_buy_threshold * 0.8
            sell_threshold = base_sell_threshold * 0.8
        elif avg_confidence > 0.5:
            buy_threshold = base_buy_threshold * 0.9
            sell_threshold = base_sell_threshold * 0.9
        else:
            buy_threshold = base_buy_threshold * 1.1
            sell_threshold = base_sell_threshold * 1.1
    else:
        buy_threshold = base_buy_threshold
        sell_threshold = base_sell_threshold

    return buy_threshold, sell_threshold