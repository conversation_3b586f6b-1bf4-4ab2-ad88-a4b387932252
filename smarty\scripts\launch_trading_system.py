#!/usr/bin/env python3
"""
Unified Trading System Launcher

Production-ready launcher that:
- Starts live dashboard and HTX data producer
- Provides CLI options to pre-select strategies
- Real-time log tailing with colored output
- Graceful shutdown handling for all processes
"""

import asyncio
import argparse
import logging
import subprocess
import signal
import sys
import time
import threading
import requests
from pathlib import Path
from typing import Dict, List, Optional
import colorama
from colorama import Fore, Back, Style

# Initialize colorama for cross-platform colored output
colorama.init(autoreset=True)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ColoredLogTailer:
    """Real-time log tailing with colored output for each component."""

    def __init__(self):
        self.colors = {
            'dashboard': Fore.CYAN,
            'data_producer': Fore.GREEN,
            'smart_model_integrated': Fore.MAGENTA,
            'smart_strategy_only': Fore.YELLOW,
            'order_flow': Fore.BLUE,
            'system': Fore.WHITE
        }
        self.running = False
        self.threads = []

    def colorize_log(self, component: str, line: str) -> str:
        """Apply color coding to log lines."""
        color = self.colors.get(component, Fore.WHITE)
        timestamp = time.strftime('%H:%M:%S')

        # Add severity color coding
        if 'ERROR' in line or '❌' in line:
            severity_color = Fore.RED + Style.BRIGHT
        elif 'WARNING' in line or '⚠️' in line:
            severity_color = Fore.YELLOW + Style.BRIGHT
        elif 'SUCCESS' in line or '✅' in line:
            severity_color = Fore.GREEN + Style.BRIGHT
        elif 'INFO' in line or '📊' in line:
            severity_color = Fore.CYAN
        else:
            severity_color = color

        return f"{Fore.WHITE}[{timestamp}] {color}[{component.upper()}]{Style.RESET_ALL} {severity_color}{line}{Style.RESET_ALL}"

    def tail_log_file(self, component: str, log_path: str):
        """Tail a log file and output with colors."""
        try:
            if not Path(log_path).exists():
                return

            with open(log_path, 'r') as f:
                # Go to end of file
                f.seek(0, 2)

                while self.running:
                    line = f.readline()
                    if line:
                        colored_line = self.colorize_log(component, line.strip())
                        print(colored_line)
                    else:
                        time.sleep(0.1)
        except Exception as e:
            print(self.colorize_log('system', f"Log tailing error for {component}: {e}"))

    def tail_process_output(self, component: str, process: subprocess.Popen):
        """Tail process stdout/stderr with colors."""
        try:
            # Skip tailing for dashboard to avoid interference
            if component == 'dashboard':
                return

            while self.running and process.poll() is None:
                # Read stdout
                if process.stdout:
                    line = process.stdout.readline()
                    if line:
                        colored_line = self.colorize_log(component, line.strip())
                        print(colored_line)

                # Read stderr
                if process.stderr:
                    line = process.stderr.readline()
                    if line:
                        colored_line = self.colorize_log(component, f"ERROR: {line.strip()}")
                        print(colored_line)

                time.sleep(0.05)
        except Exception as e:
            print(self.colorize_log('system', f"Process output tailing error for {component}: {e}"))

    def start_tailing(self, processes: Dict[str, subprocess.Popen]):
        """Start tailing all processes and log files."""
        self.running = True

        # Tail process outputs
        for component, process in processes.items():
            thread = threading.Thread(
                target=self.tail_process_output,
                args=(component, process),
                daemon=True
            )
            thread.start()
            self.threads.append(thread)

        # Tail log files (if they exist)
        log_files = {
            'smart_model_integrated': 'logs/smart_model_integrated.log',
            'smart_strategy_only': 'logs/smart_strategy_only.log',
            'order_flow': 'logs/order_flow.log'
        }

        for component, log_path in log_files.items():
            if Path(log_path).exists():
                thread = threading.Thread(
                    target=self.tail_log_file,
                    args=(component, log_path),
                    daemon=True
                )
                thread.start()
                self.threads.append(thread)

    def stop_tailing(self):
        """Stop all log tailing."""
        self.running = False
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=1)


class UnifiedTradingSystemLauncher:
    """Production-ready unified system launcher."""

    def __init__(self, strategy: Optional[str] = None):
        self.strategy = strategy
        self.processes: Dict[str, subprocess.Popen] = {}
        self.running = False
        self.log_tailer = ColoredLogTailer()

        # Core components
        self.components = {
            'data_producer': {
                'command': 'python feeds/htx_data_producer.py',
                'description': 'HTX Market Data Producer',
                'startup_delay': 3,
                'required': True
            },
            'dashboard': {
                'command': 'python live_dashboard.py',
                'description': 'Live Trading Dashboard',
                'startup_delay': 2,  # Reduced from 5 to 2 seconds for faster startup
                'required': True
            }
        }

        # Strategy commands
        self.strategy_commands = {
            'smart_model_integrated': 'python orchestrator.py --debug',
            'smart_strategy_only': 'python run_smart_strategy_live.py',
            'order_flow': 'python live_dataframe_strategy_runner.py'
        }

    def print_banner(self):
        """Print startup banner."""
        banner = f"""
{Fore.CYAN + Style.BRIGHT}
╔══════════════════════════════════════════════════════════════╗
║                    🎯 UNIFIED TRADING SYSTEM                 ║
║                      Production Launcher                     ║
╚══════════════════════════════════════════════════════════════╝
{Style.RESET_ALL}
{Fore.GREEN}🚀 Starting Components:{Style.RESET_ALL}
  • HTX Market Data Producer (Live Data Feed)
  • Enhanced Trading Dashboard (localhost:8082)
  • Real-time Log Monitoring (Colored Output)
  • Graceful Shutdown Handling
"""
        if self.strategy:
            banner += f"\n{Fore.YELLOW}🎯 Pre-selected Strategy: {self.strategy.replace('_', ' ').title()}{Style.RESET_ALL}"

        banner += f"\n{Fore.WHITE}{'='*60}{Style.RESET_ALL}\n"
        print(banner)

    def check_prerequisites(self) -> bool:
        """Check system prerequisites."""
        print(f"{Fore.CYAN}🔍 Checking Prerequisites...{Style.RESET_ALL}")

        required_files = [
            'live_dashboard.py',
            'feeds/htx_data_producer.py',
            'orchestrator.py',
            'run_smart_strategy_live.py',
            'live_dataframe_strategy_runner.py'
        ]

        missing_files = []
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)

        if missing_files:
            print(f"{Fore.RED}❌ Missing required files: {missing_files}{Style.RESET_ALL}")
            return False

        # Ensure directories exist
        for directory in ['logs', 'data', 'scripts']:
            Path(directory).mkdir(exist_ok=True)

        print(f"{Fore.GREEN}✅ Prerequisites check passed{Style.RESET_ALL}")
        return True

    def verify_dashboard_accessibility(self) -> bool:
        """Verify that the dashboard is accessible."""
        try:
            # Give dashboard a moment to fully initialize
            time.sleep(3)
            response = requests.get('http://localhost:8082', timeout=5)
            return response.status_code in [200, 302]  # 200 for direct access, 302 for redirect to login
        except Exception:
            return False

    def start_component(self, component_name: str) -> bool:
        """Start a system component."""
        component = self.components[component_name]

        try:
            print(f"{Fore.YELLOW}🚀 Starting {component['description']}...{Style.RESET_ALL}")

            # Special handling for dashboard to preserve authentication
            if component_name == 'dashboard':
                # Start dashboard without capturing output to preserve authentication flow
                process = subprocess.Popen(
                    component['command'].split(),
                    cwd=Path.cwd(),  # Ensure correct working directory
                    env=None,  # Inherit environment
                    # Don't capture stdout/stderr for dashboard to preserve normal operation
                )
            else:
                # For other components, capture output for monitoring
                process = subprocess.Popen(
                    component['command'].split(),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,
                    universal_newlines=True,
                    cwd=Path.cwd()
                )

            # Wait for startup
            time.sleep(component.get('startup_delay', 2))

            if process.poll() is None:
                self.processes[component_name] = process

                # Special verification for dashboard
                if component_name == 'dashboard':
                    if self.verify_dashboard_accessibility():
                        print(f"{Fore.GREEN}✅ {component['description']} started (PID: {process.pid}) - Dashboard accessible{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.YELLOW}⚠️ {component['description']} started (PID: {process.pid}) - Dashboard may need a moment to initialize{Style.RESET_ALL}")
                else:
                    print(f"{Fore.GREEN}✅ {component['description']} started (PID: {process.pid}){Style.RESET_ALL}")
                return True
            else:
                if component_name != 'dashboard':  # Only try to get output for non-dashboard components
                    try:
                        stdout, stderr = process.communicate(timeout=1)
                        print(f"{Fore.RED}❌ {component['description']} failed to start{Style.RESET_ALL}")
                        if stderr:
                            print(f"{Fore.RED}   Error: {stderr[:200]}...{Style.RESET_ALL}")
                    except subprocess.TimeoutExpired:
                        print(f"{Fore.RED}❌ {component['description']} failed to start (timeout){Style.RESET_ALL}")
                else:
                    print(f"{Fore.RED}❌ {component['description']} failed to start{Style.RESET_ALL}")
                return False

        except Exception as e:
            print(f"{Fore.RED}❌ Failed to start {component['description']}: {e}{Style.RESET_ALL}")
            return False

    def start_strategy(self, strategy_name: str) -> bool:
        """Start a specific strategy."""
        if strategy_name not in self.strategy_commands:
            print(f"{Fore.RED}❌ Unknown strategy: {strategy_name}{Style.RESET_ALL}")
            return False

        try:
            command = self.strategy_commands[strategy_name]
            print(f"{Fore.MAGENTA}🎯 Starting strategy: {strategy_name.replace('_', ' ').title()}...{Style.RESET_ALL}")

            process = subprocess.Popen(
                command.split(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Wait for strategy startup
            time.sleep(3)

            if process.poll() is None:
                self.processes[strategy_name] = process
                print(f"{Fore.GREEN}✅ Strategy {strategy_name.replace('_', ' ').title()} started (PID: {process.pid}){Style.RESET_ALL}")
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"{Fore.RED}❌ Strategy {strategy_name} failed to start{Style.RESET_ALL}")
                if stderr:
                    print(f"{Fore.RED}   Error: {stderr[:200]}...{Style.RESET_ALL}")
                return False

        except Exception as e:
            print(f"{Fore.RED}❌ Failed to start strategy {strategy_name}: {e}{Style.RESET_ALL}")
            return False

    def display_status(self):
        """Display system status."""
        print(f"\n{Fore.CYAN + Style.BRIGHT}📊 SYSTEM STATUS{Style.RESET_ALL}")
        print(f"{Fore.WHITE}{'='*50}{Style.RESET_ALL}")

        # Core components
        print(f"{Fore.CYAN}🔧 Core Components:{Style.RESET_ALL}")
        for component_name, component in self.components.items():
            if component_name in self.processes:
                process = self.processes[component_name]
                if process.poll() is None:
                    status = f"{Fore.GREEN}✅ RUNNING (PID: {process.pid}){Style.RESET_ALL}"
                else:
                    status = f"{Fore.RED}❌ STOPPED{Style.RESET_ALL}"
            else:
                status = f"{Fore.YELLOW}⚪ NOT STARTED{Style.RESET_ALL}"

            print(f"  {component['description']}: {status}")

        # Active strategies
        strategy_count = sum(1 for name in self.processes if name in self.strategy_commands)
        if strategy_count > 0:
            print(f"\n{Fore.MAGENTA}🎯 Active Strategies:{Style.RESET_ALL}")
            for strategy_name in self.strategy_commands:
                if strategy_name in self.processes:
                    process = self.processes[strategy_name]
                    if process.poll() is None:
                        status = f"{Fore.GREEN}✅ RUNNING (PID: {process.pid}){Style.RESET_ALL}"
                        print(f"  {strategy_name.replace('_', ' ').title()}: {status}")

        # Access information
        print(f"\n{Fore.GREEN}🌐 Access Information:{Style.RESET_ALL}")
        print(f"  Dashboard URL: {Fore.CYAN}http://localhost:8082{Style.RESET_ALL}")
        print(f"  Login: {Fore.YELLOW}epinnox / securepass123{Style.RESET_ALL}")

        print(f"{Fore.WHITE}{'='*50}{Style.RESET_ALL}")

    async def start_system(self) -> bool:
        """Start the complete trading system."""
        self.print_banner()

        # Check prerequisites
        if not self.check_prerequisites():
            return False

        # Start core components
        startup_order = ['data_producer', 'dashboard']

        for component_name in startup_order:
            if not self.start_component(component_name):
                print(f"{Fore.RED}❌ Failed to start {component_name}. Aborting.{Style.RESET_ALL}")
                await self.stop_system()
                return False

        # Start pre-selected strategy if specified
        if self.strategy:
            if not self.start_strategy(self.strategy):
                print(f"{Fore.YELLOW}⚠️ Failed to start pre-selected strategy, continuing with dashboard only{Style.RESET_ALL}")

        self.running = True

        # Display status
        self.display_status()

        # Start log tailing
        print(f"\n{Fore.CYAN}📡 Starting real-time log monitoring...{Style.RESET_ALL}")
        self.log_tailer.start_tailing(self.processes)

        # Monitor system
        await self.monitor_system()

        return True

    async def monitor_system(self):
        """Monitor system health."""
        try:
            while self.running:
                await asyncio.sleep(30)  # Check every 30 seconds

                # Check for failed processes
                failed_components = []
                for component_name, process in list(self.processes.items()):
                    if process.poll() is not None:
                        failed_components.append(component_name)
                        del self.processes[component_name]

                if failed_components:
                    for component in failed_components:
                        print(f"{Fore.RED}⚠️ Component {component} has stopped unexpectedly{Style.RESET_ALL}")

        except asyncio.CancelledError:
            pass

    async def stop_system(self):
        """Gracefully stop all components."""
        print(f"\n{Fore.YELLOW}🛑 Initiating graceful shutdown...{Style.RESET_ALL}")
        self.running = False

        # Stop log tailing
        self.log_tailer.stop_tailing()

        # Stop all processes
        for component_name, process in self.processes.items():
            try:
                print(f"{Fore.YELLOW}🛑 Stopping {component_name}...{Style.RESET_ALL}")
                process.terminate()

                try:
                    process.wait(timeout=10)
                    print(f"{Fore.GREEN}✅ {component_name} stopped gracefully{Style.RESET_ALL}")
                except subprocess.TimeoutExpired:
                    print(f"{Fore.YELLOW}⚠️ Force killing {component_name}...{Style.RESET_ALL}")
                    process.kill()
                    process.wait()
                    print(f"{Fore.GREEN}✅ {component_name} force stopped{Style.RESET_ALL}")

            except Exception as e:
                print(f"{Fore.RED}❌ Error stopping {component_name}: {e}{Style.RESET_ALL}")

        print(f"{Fore.GREEN}✅ System shutdown complete{Style.RESET_ALL}")

    def signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        print(f"\n{Fore.YELLOW}📡 Received signal {signum}. Initiating shutdown...{Style.RESET_ALL}")
        self.running = False


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Unified Trading System Launcher')
    parser.add_argument(
        '--strategy',
        choices=['smart_model_integrated', 'smart_strategy_only', 'order_flow'],
        help='Pre-select and start a specific strategy'
    )
    parser.add_argument(
        '--no-color',
        action='store_true',
        help='Disable colored output'
    )

    args = parser.parse_args()

    # Disable colors if requested
    if args.no_color:
        colorama.deinit()

    launcher = UnifiedTradingSystemLauncher(strategy=args.strategy)

    # Set up signal handlers
    signal.signal(signal.SIGINT, launcher.signal_handler)
    signal.signal(signal.SIGTERM, launcher.signal_handler)

    try:
        success = await launcher.start_system()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}🛑 Keyboard interrupt received{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}❌ Unexpected error: {e}{Style.RESET_ALL}")
    finally:
        await launcher.stop_system()


if __name__ == "__main__":
    asyncio.run(main())
