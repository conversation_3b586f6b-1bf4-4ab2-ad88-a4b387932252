#!/usr/bin/env python3
"""
Minimal Dashboard Test - Identify startup issues
"""

import asyncio
import logging
from aiohttp import web
import aiohttp_cors

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MinimalDashboard:
    """Minimal dashboard for testing."""
    
    def __init__(self):
        logger.info("🔍 Initializing minimal dashboard...")
    
    async def serve_dashboard(self, request):
        """Serve a simple dashboard page."""
        html = """
<!DOCTYPE html>
<html>
<head>
    <title>🎯 Smart Trader Dashboard - WORKING!</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: #0f1419; 
            color: #fff; 
            padding: 20px; 
            text-align: center;
        }
        .status { 
            background: #1e2328; 
            padding: 20px; 
            border-radius: 10px; 
            margin: 20px 0;
        }
        .success { color: #00ff88; }
        .info { color: #00aaff; }
    </style>
</head>
<body>
    <h1>🎯 Smart Trader Live Dashboard</h1>
    <div class="status">
        <h2 class="success">✅ DASHBOARD IS WORKING!</h2>
        <p class="info">The live trading dashboard is now operational.</p>
        <p>Timestamp: <span id="timestamp"></span></p>
    </div>
    
    <div class="status">
        <h3>🚀 Production Features Ready:</h3>
        <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
            <li>✅ Unified System Launcher</li>
            <li>✅ Strategy Health Monitoring</li>
            <li>✅ Centralized Logging System</li>
            <li>✅ Enhanced Trade Execution Safety</li>
            <li>✅ Real-time Log Monitoring</li>
            <li>✅ Graceful Shutdown Handling</li>
        </ul>
    </div>
    
    <div class="status">
        <h3>🔐 Authentication</h3>
        <p>Login: <strong>epinnox</strong> / <strong>securepass123</strong></p>
    </div>
    
    <script>
        document.getElementById('timestamp').textContent = new Date().toISOString();
        
        // Auto-refresh timestamp every second
        setInterval(() => {
            document.getElementById('timestamp').textContent = new Date().toISOString();
        }, 1000);
    </script>
</body>
</html>
        """
        return web.Response(text=html, content_type='text/html')
    
    async def api_health(self, request):
        """Simple health check API."""
        return web.json_response({
            "status": "operational",
            "timestamp": asyncio.get_event_loop().time(),
            "message": "Dashboard is working correctly!"
        })
    
    async def start_server(self, host: str = "localhost", port: int = 8082):
        """Start the minimal dashboard server."""
        logger.info(f"🚀 Starting minimal dashboard on {host}:{port}")
        
        app = web.Application()
        
        # Setup CORS
        cors = aiohttp_cors.setup(app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        # Routes
        app.router.add_get('/', self.serve_dashboard)
        app.router.add_get('/dashboard', self.serve_dashboard)
        app.router.add_get('/api/health', self.api_health)
        
        # Add CORS to all routes
        for route in list(app.router.routes()):
            cors.add(route)
        
        # Start server
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, host, port)
        await site.start()
        
        logger.info(f"✅ Minimal dashboard running at http://{host}:{port}")
        logger.info("🎯 Test the dashboard by opening http://localhost:8082 in your browser")
        
        # Keep running
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 Minimal dashboard shutting down...")
        finally:
            await runner.cleanup()


async def main():
    """Main entry point."""
    dashboard = MinimalDashboard()
    await dashboard.start_server()


if __name__ == "__main__":
    asyncio.run(main())
