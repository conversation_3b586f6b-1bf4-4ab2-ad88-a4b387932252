OptimizedSQLiteBus not available, falling back to standard SQLiteBus
2025-05-29 12:48:05,937 - __main__ - INFO - \U0001f680 Starting Market Data Producer for Smart-Trader Dashboard...
2025-05-29 12:48:05,952 - pipeline.databus - INFO - SQLiteBus initialized with database at C:\Users\<USER>\Documents\dev\smarty\data\bus.db
2025-05-29 12:48:05,952 - __main__ - INFO - \U0001f504 Attempting HTX connection...
2025-05-29 12:48:05,952 - feeds.htx.client - INFO - HTX Futures Client v2.0.0 initialized
2025-05-29 12:48:05,952 - feeds.htx_futures - INFO - HTX Futures Client (backward-compatible wrapper) initialized
2025-05-29 12:48:05,955 - feeds.htx.client - INFO - Message bus publisher set for HTX Futures client
2025-05-29 12:48:05,956 - feeds.htx_futures - INFO - Message bus publisher set for HTX Futures client
2025-05-29 12:48:05,956 - feeds.htx.client - INFO - Connecting to HTX Futures API...
2025-05-29 12:48:06,192 - feeds.htx.websocket - ERROR - Failed to connect to market WebSocket: Cannot connect to host api-usdt.linear.contract.huobi.pro:443 ssl:default [DNS server returned answer with no data]
2025-05-29 12:48:06,192 - feeds.htx.client - ERROR - Failed to connect to HTX Futures API: Market WebSocket connection failed: Cannot connect to host api-usdt.linear.contract.huobi.pro:443 ssl:default [DNS server returned answer with no data]
2025-05-29 12:48:06,192 - feeds.htx.client - INFO - Closing HTX Futures client...
2025-05-29 12:48:06,192 - feeds.htx.websocket - INFO - Closing HTX WebSocket connections...
2025-05-29 12:48:06,192 - feeds.htx.websocket - INFO - HTX WebSocket connections closed
2025-05-29 12:48:06,192 - feeds.htx.client - INFO - HTX Futures client closed
2025-05-29 12:48:06,192 - __main__ - ERROR - \u274c HTX connection failed: Connection failed: Market WebSocket connection failed: Cannot connect to host api-usdt.linear.contract.huobi.pro:443 ssl:default [DNS server returned answer with no data]
2025-05-29 12:48:06,192 - __main__ - WARNING - \u26a0\ufe0f HTX connection failed, switching to Binance fallback
2025-05-29 12:48:06,192 - __main__ - INFO - \U0001f504 Attempting Binance fallback connection...
2025-05-29 12:48:06,192 - feeds.binance_fallback_client - INFO - Message bus publisher set for Binance fallback client
2025-05-29 12:48:06,193 - feeds.binance_fallback_client - INFO - \U0001f504 Connecting to Binance WebSocket (HTX fallback)...
2025-05-29 12:48:07,882 - feeds.binance_fallback_client - INFO - \u2705 Connected to Binance WebSocket
2025-05-29 12:48:07,882 - feeds.binance_fallback_client - INFO - \U0001f4ca Subscribed to 6 Binance streams
2025-05-29 12:48:07,882 - __main__ - INFO - \u2705 Connected to Binance WebSocket (fallback)
2025-05-29 12:48:07,882 - __main__ - INFO - \u2705 Using Binance as fallback data source
2025-05-29 12:48:07,882 - __main__ - INFO - \U0001f3af Market Data Producer started for 2 symbols
2025-05-29 12:48:07,882 - __main__ - INFO - \U0001f4ca Real market data is now flowing to data/bus.db
2025-05-29 12:48:07,882 - __main__ - INFO - \U0001f310 Dashboard should now show live market data!
2025-05-29 12:49:28,999 - __main__ - INFO - \U0001f4c8 Binance fallback active - 60 seconds uptime
2025-05-29 12:50:40,746 - __main__ - INFO - \U0001f4c8 Binance fallback active - 120 seconds uptime
2025-05-29 12:51:47,578 - __main__ - INFO - \U0001f4c8 Binance fallback active - 180 seconds uptime
2025-05-29 12:53:01,543 - __main__ - INFO - \U0001f4c8 Binance fallback active - 240 seconds uptime
2025-05-29 12:54:45,877 - __main__ - INFO - \U0001f4c8 Binance fallback active - 300 seconds uptime
2025-05-29 12:56:07,753 - __main__ - INFO - \U0001f4c8 Binance fallback active - 360 seconds uptime
2025-05-29 12:57:18,209 - __main__ - INFO - \U0001f4c8 Binance fallback active - 420 seconds uptime
