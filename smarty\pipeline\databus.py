"""
SQLite-backed message bus for the smart-trader system.

This module provides a durable message bus implementation using SQLite,
allowing components to communicate through a publish-subscribe pattern
with message persistence.
"""

import sqlite3
import time
import json
import logging
import os
import asyncio
from threading import Thread, Event
from typing import Dict, List, Callable, Any, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class SQLiteBus:
    """
    SQLite-backed message bus for the smart-trader system.

    This class provides a durable publish-subscribe mechanism using SQLite
    as the backing store. Messages are persisted to disk and can be
    replayed after system restarts.
    """

    def __init__(self, path: str = "data/bus.db", poll_interval: float = 0.5):
        """
        Initialize the SQLite message bus.

        Args:
            path: Path to the SQLite database file
            poll_interval: How often to check for new messages (seconds)
        """
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(path), exist_ok=True)

        self.path = path
        self.poll_interval = poll_interval
        self.conn = sqlite3.connect(path, check_same_thread=False)
        self._init_schema()

        # Stream name -> list of callbacks
        self._subs: Dict[str, List[Callable[[float, dict], None]]] = {}

        # For stopping the polling thread
        self._stop = Event()

        # Start the polling thread
        self._poller_thread = Thread(
            target=self._poller,
            args=(poll_interval,),
            daemon=True,
            name="SQLiteBus-Poller"
        )
        self._poller_thread.start()

        logger.info(f"SQLiteBus initialized with database at {path}")

    def _init_schema(self) -> None:
        """Initialize the database schema."""
        c = self.conn.cursor()
        c.execute("""
            CREATE TABLE IF NOT EXISTS messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stream TEXT NOT NULL,
                ts REAL NOT NULL,
                payload TEXT NOT NULL
            )
        """)

        # Create index on stream for faster lookups
        c.execute("""
            CREATE INDEX IF NOT EXISTS idx_messages_stream ON messages(stream)
        """)

        # Create index on timestamp for cleanup operations
        c.execute("""
            CREATE INDEX IF NOT EXISTS idx_messages_ts ON messages(ts)
        """)

        self.conn.commit()

    def publish(self, stream: str, ts: float, payload: dict) -> None:
        """
        Publish a message to a stream.

        Args:
            stream: Stream name (e.g., "htx.trades", "signalstar.sentiment")
            ts: Timestamp (Unix timestamp)
            payload: Message payload (must be JSON-serializable)
        """
        try:
            c = self.conn.cursor()
            c.execute(
                "INSERT INTO messages(stream, ts, payload) VALUES(?,?,?)",
                (stream, ts, json.dumps(payload))
            )
            self.conn.commit()
            logger.debug(f"Published message to {stream}: {payload}")
        except Exception as e:
            logger.error(f"Error publishing to {stream}: {e}")
            # Ensure the connection is still usable
            self.conn.rollback()

    def subscribe(self, stream: str, callback: Callable[[float, dict], None]) -> None:
        """
        Subscribe to a stream.

        Args:
            stream: Stream name to subscribe to
            callback: Function to call when a message is received
                      The callback receives (timestamp, payload)
        """
        if stream not in self._subs:
            self._subs[stream] = []

        self._subs[stream].append(callback)
        logger.info(f"Added subscription to {stream}")

    def unsubscribe(self, stream: str, callback: Callable[[float, dict], None]) -> bool:
        """
        Unsubscribe from a stream.

        Args:
            stream: Stream name to unsubscribe from
            callback: Callback function to remove

        Returns:
            True if the subscription was removed, False otherwise
        """
        if stream in self._subs and callback in self._subs[stream]:
            self._subs[stream].remove(callback)
            logger.info(f"Removed subscription from {stream}")
            return True
        return False

    def _poller(self, interval: float) -> None:
        """
        Poll for new messages and dispatch to subscribers.

        Args:
            interval: How often to poll (seconds)
        """
        last_id = 0

        while not self._stop.is_set():
            try:
                # Create a fresh cursor for each polling cycle to avoid cursor invalidation
                c = self.conn.cursor()
                try:
                    c.execute(
                        "SELECT id, stream, ts, payload FROM messages WHERE id > ? ORDER BY id",
                        (last_id,)
                    )

                    # Fetch all results to avoid cursor issues during iteration
                    results = c.fetchall()

                    for mid, stream, ts, raw in results:
                        last_id = mid

                        if stream in self._subs:
                            try:
                                obj = json.loads(raw)
                                for cb in self._subs[stream]:
                                    try:
                                        cb(ts, obj)
                                    except Exception as e:
                                        logger.error(f"Error in subscriber callback for {stream}: {e}")
                            except json.JSONDecodeError as e:
                                logger.error(f"Error decoding message payload for {stream}: {e}")
                finally:
                    # Always close the cursor
                    c.close()

            except Exception as e:
                logger.error(f"Error in message poller: {e}")
                # Sleep a bit longer on error to avoid tight error loops
                time.sleep(interval * 2)

            time.sleep(interval)

    def cleanup_old_messages(self, max_age_days: float = 7.0) -> int:
        """
        Delete messages older than the specified age.

        Args:
            max_age_days: Maximum age of messages to keep (in days)

        Returns:
            Number of messages deleted
        """
        cutoff = time.time() - (max_age_days * 86400)

        try:
            c = self.conn.cursor()
            c.execute("DELETE FROM messages WHERE ts < ?", (cutoff,))
            deleted = c.rowcount
            self.conn.commit()

            logger.info(f"Cleaned up {deleted} messages older than {max_age_days} days")
            return deleted

        except Exception as e:
            logger.error(f"Error cleaning up old messages: {e}")
            self.conn.rollback()
            return 0

    def get_message_count(self, stream: Optional[str] = None) -> int:
        """
        Get the count of messages in the database.

        Args:
            stream: Optional stream name to filter by

        Returns:
            Number of messages
        """
        try:
            c = self.conn.cursor()

            if stream:
                c.execute("SELECT COUNT(*) FROM messages WHERE stream = ?", (stream,))
            else:
                c.execute("SELECT COUNT(*) FROM messages")

            return c.fetchone()[0]

        except Exception as e:
            logger.error(f"Error getting message count: {e}")
            return 0

    def close(self) -> None:
        """Close the message bus and release resources."""
        logger.info("Shutting down SQLiteBus...")
        self._stop.set()

        # Wait for the poller thread to stop
        if self._poller_thread.is_alive():
            self._poller_thread.join(timeout=2.0)

        # Close the database connection
        if self.conn:
            self.conn.close()

        logger.info("SQLiteBus shut down")


class InMemoryBus:
    """
    In-memory message bus implementation.

    This class provides a non-persistent publish-subscribe mechanism
    for testing or low-durability scenarios.
    """

    def __init__(self):
        """Initialize the in-memory message bus."""
        self._subs: Dict[str, List[Callable[[float, dict], None]]] = {}
        self._messages: List[tuple] = []  # For testing/inspection
        logger.info("InMemoryBus initialized")

    def publish(self, stream: str, ts: float, payload: dict) -> None:
        """
        Publish a message to a stream.

        Args:
            stream: Stream name
            ts: Timestamp
            payload: Message payload
        """
        self._messages.append((stream, ts, payload))

        if stream in self._subs:
            for cb in self._subs[stream]:
                try:
                    cb(ts, payload)
                except Exception as e:
                    logger.error(f"Error in subscriber callback for {stream}: {e}")

    def subscribe(self, stream: str, callback: Callable[[float, dict], None]) -> None:
        """
        Subscribe to a stream.

        Args:
            stream: Stream name to subscribe to
            callback: Function to call when a message is received
        """
        if stream not in self._subs:
            self._subs[stream] = []

        self._subs[stream].append(callback)

    def unsubscribe(self, stream: str, callback: Callable[[float, dict], None]) -> bool:
        """
        Unsubscribe from a stream.

        Args:
            stream: Stream name to unsubscribe from
            callback: Callback function to remove

        Returns:
            True if the subscription was removed, False otherwise
        """
        if stream in self._subs and callback in self._subs[stream]:
            self._subs[stream].remove(callback)
            return True
        return False

    def get_message_count(self, stream: Optional[str] = None) -> int:
        """
        Get the count of messages.

        Args:
            stream: Optional stream name to filter by

        Returns:
            Number of messages
        """
        if stream:
            return sum(1 for s, _, _ in self._messages if s == stream)
        return len(self._messages)

    def close(self) -> None:
        """Close the message bus (no-op for in-memory bus)."""
        self._subs.clear()
        logger.info("InMemoryBus shut down")


# Import the optimized SQLiteBus
try:
    from optimized_databus import OptimizedSQLiteBus
    OPTIMIZED_BUS_AVAILABLE = True
except ImportError:
    OPTIMIZED_BUS_AVAILABLE = False
    logger.warning("OptimizedSQLiteBus not available, falling back to standard SQLiteBus")


# Factory function to create the appropriate bus based on configuration
def create_bus(config: dict) -> Union[SQLiteBus, InMemoryBus, 'OptimizedSQLiteBus']:
    """
    Create a message bus based on configuration.

    Args:
        config: Configuration dictionary

    Returns:
        Message bus instance
    """
    bus_type = config.get("pipeline", {}).get("bus", "sqlite")

    if bus_type == "in_memory":
        return InMemoryBus()
    elif bus_type == "optimized_sqlite" and OPTIMIZED_BUS_AVAILABLE:
        # Create optimized SQLiteBus
        optimized_config = config.get("pipeline", {}).get("optimized_sqlite", {})
        path = optimized_config.get("path", "data/optimized_bus.db")
        poll_interval = optimized_config.get("poll_interval", 0.1)
        batch_size = optimized_config.get("batch_size", 50)
        batch_timeout = optimized_config.get("batch_timeout", 0.5)

        logger.info(f"Creating OptimizedSQLiteBus with path={path}, poll_interval={poll_interval}, "
                   f"batch_size={batch_size}, batch_timeout={batch_timeout}")

        return OptimizedSQLiteBus(
            path=path,
            poll_interval=poll_interval,
            batch_size=batch_size,
            batch_timeout=batch_timeout
        )
    else:  # Default to standard SQLite
        if bus_type == "optimized_sqlite" and not OPTIMIZED_BUS_AVAILABLE:
            logger.warning("OptimizedSQLiteBus requested but not available, falling back to standard SQLiteBus")

        sqlite_config = config.get("pipeline", {}).get("sqlite", {})
        path = sqlite_config.get("path", "data/bus.db")
        poll_interval = sqlite_config.get("poll_interval", 0.5)

        logger.info(f"Creating SQLiteBus with path={path}, poll_interval={poll_interval}")

        return SQLiteBus(path=path, poll_interval=poll_interval)
