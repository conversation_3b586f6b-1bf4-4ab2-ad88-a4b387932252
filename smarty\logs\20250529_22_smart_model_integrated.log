2025-05-29 22:05:08,917 - strategy.smart_model_integrated - INFO - [info:89] - 🚀 Strategy logger initialized for smart_model_integrated
2025-05-29 22:05:08,918 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Log file: logs\20250529_22_smart_model_integrated.log
2025-05-29 22:05:08,918 - strategy.smart_model_integrated - INFO - [info:89] - 📊 JSON events: logs\20250529_22_smart_model_integrated_events.json
2025-05-29 22:05:08,918 - strategy.smart_model_integrated - INFO - [info:89] - 🎯 Strategy: smart_model_integrated
2025-05-29 22:05:08,918 - strategy.smart_model_integrated - INFO - [info:89] - 📅 Session: 2025-05-29 22:05:08
2025-05-29 22:05:08,921 - strategy.smart_model_integrated - INFO - [info:89] - 🔧 Log level: INFO
2025-05-29 22:05:08,921 - strategy.smart_model_integrated - INFO - [info:89] - 💻 Python: 3.13.2 | packaged by <PERSON><PERSON>da, Inc. | (main, Feb  6 2025, 18:49:14) [MSC v.1929 64 bit (AMD64)]
2025-05-29 22:05:08,922 - strategy.smart_model_integrated - INFO - [info:89] - 📁 Working directory: C:\Users\<USER>\Documents\dev\smarty
2025-05-29 22:05:08,923 - strategy.smart_model_integrated - INFO - [info:89] - 🐛 Debug mode enabled
2025-05-29 22:05:08,923 - strategy.smart_model_integrated - INFO - [info:89] - 📊 Strategy: smart_integrated
2025-05-29 22:05:08,923 - strategy.smart_model_integrated - INFO - [info:89] - 💰 Symbol: BTC-USDT
2025-05-29 22:05:08,923 - strategy.smart_model_integrated - INFO - [info:89] - 🧪 Testnet: False
2025-05-29 22:05:08,923 - strategy.smart_model_integrated - INFO - [info:89] - 💸 Trading: False
2025-05-29 22:05:08,941 - strategy.smart_model_integrated - INFO - [info:89] - Initialized message bus: SQLiteBus
2025-05-29 22:05:08,942 - strategy.smart_model_integrated - INFO - [info:89] - Set HTX client simulation mode: True
2025-05-29 22:05:08,942 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for HTX client
2025-05-29 22:05:08,942 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Multi-Exchange client
2025-05-29 22:05:08,942 - strategy.smart_model_integrated - INFO - [info:89] - Set publisher for Binance fallback client
2025-05-29 22:05:08,951 - strategy.smart_model_integrated - WARNING - [warning:93] - SignalStar client not initialized, social sentiment model disabled
2025-05-29 22:05:08,954 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Enhanced LLM Consumer initialized successfully
2025-05-29 22:05:08,960 - strategy.smart_model_integrated - INFO - [info:89] - Starting orchestrator...
2025-05-29 22:05:08,960 - strategy.smart_model_integrated - INFO - [info:89] - 🔄 Attempting Multi-Exchange connection...
2025-05-29 22:05:12,508 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Connected to Multi-Exchange client
2025-05-29 22:05:12,510 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Using Multi-Exchange as primary data source
2025-05-29 22:05:12,511 - strategy.smart_model_integrated - INFO - [info:89] - ✅ Set up message bus subscriptions for Binance fallback data
