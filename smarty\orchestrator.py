"""
Orchestrator for the smart-trader system.

This is the main entry point for the system, coordinating all components:
- Market data feeds
- Feature calculation
- Model predictions
- LLM analysis
- Order execution
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Set, Tuple

from core.events import (
    Kline, Trade, OrderbookDelta, Position, Signal, Side,
    FeatureSnapshot, LLMResponse, FundingRateEvent, OpenInterestEvent
)
from core.feature_store import feature_store
from core.rule_engine import RuleEngine
from core.utils import setup_logging
from core.serialization import signal_to_dict
from feeds.htx_futures import HTXFuturesClient
from feeds.binance_fallback_client import BinanceFallbackClient
from feeds.multi_exchange_client import MultiExchangeClient
from feeds.htx_funding import fetch_funding_rate, fetch_historical_funding_rates
from feeds.htx_open_interest import fetch_open_interest

# Import CCXT integration for multi-exchange support
CCXT_AVAILABLE = False
CCXTManager = None
try:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), 'epinnox'))
    from ccxt_integration import CCXTManager
    CCXT_AVAILABLE = True
except ImportError:
    CCXT_AVAILABLE = False
from models.rsi import RSIModel
from models.orderflow_net import OrderFlowNet
from llm.enhanced_llm_consumer import EnhancedLLMConsumer
from executors.htx_executor import HTXExecutor
from clients.signalstar_client import SignalStarClient
from pipeline.databus import create_bus


logger = logging.getLogger(__name__)


class Orchestrator:
    """
    Main orchestrator for the smart-trader system.

    Coordinates all components and manages the event loop.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the orchestrator.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.symbols = config.get("symbols", ["BTC-USDT"])
        self.running = False
        self.last_llm_call_time: Dict[str, datetime] = {}
        self.llm_throttle_seconds = config.get("llm_throttle_seconds", 5)

        # Trading control
        trading_config = config.get("trading", {})
        self.trading_enabled = trading_config.get("enabled", False)
        self.simulation_mode = trading_config.get("simulation_mode", True)
        self.max_positions = trading_config.get("max_positions", 1)
        self.active_positions: Set[str] = set()

        # Data source fallback control
        self.using_fallback = False
        self.primary_client = None
        self.fallback_client = None

        # Initialize message bus
        self.bus = create_bus(config)
        logger.info(f"Initialized message bus: {type(self.bus).__name__}")

        # Initialize components
        self._init_components()

        # Event tracking
        self.last_kline_timestamps: Dict[str, datetime] = {}
        self.last_trade_timestamps: Dict[str, datetime] = {}
        self.last_feature_update = datetime.now()
        self.feature_update_interval = timedelta(seconds=1)

    def _init_components(self) -> None:
        """Initialize all system components."""
        # Initialize HTX client (primary)
        self.htx_client = HTXFuturesClient(
            api_key=self.config.get("api_key", ""),
            api_secret=self.config.get("api_secret", ""),
            testnet=self.config.get("testnet", False)
        )

        # Set simulation mode on the client
        if hasattr(self.htx_client, 'simulation_mode'):
            self.htx_client.simulation_mode = self.simulation_mode
            logger.info(f"Set HTX client simulation mode: {self.simulation_mode}")

        # Set publisher for HTX client
        if hasattr(self.htx_client, 'set_publisher'):
            self.htx_client.set_publisher(self.bus.publish)
            logger.info("Set publisher for HTX client")

        # Initialize Multi-Exchange client (primary)
        self.multi_exchange_client = MultiExchangeClient()
        if hasattr(self.multi_exchange_client, 'set_publisher'):
            self.multi_exchange_client.set_publisher(self.bus.publish)
            logger.info("Set publisher for Multi-Exchange client")

        # Initialize Binance fallback client (secondary fallback)
        self.binance_client = BinanceFallbackClient()
        if hasattr(self.binance_client, 'set_publisher'):
            self.binance_client.set_publisher(self.bus.publish)
            logger.info("Set publisher for Binance fallback client")

        # Set primary and fallback clients
        self.primary_client = self.multi_exchange_client  # Use multi-exchange as primary
        self.fallback_client = self.binance_client

        # Initialize clients dictionary
        self.clients = {}
        self.clients["htx"] = self.htx_client
        self.clients["binance"] = self.binance_client

        # Initialize SignalStar client if API key is provided
        signalstar_config = self.config.get("signalstar", {})
        if signalstar_config.get("api_key"):
            signalstar_client = SignalStarClient(
                api_key=signalstar_config.get("api_key", ""),
                base_url=signalstar_config.get("base_url", "https://api.signalstar.com/v1")
            )

            # Set publisher for SignalStar client
            if hasattr(signalstar_client, 'set_publisher'):
                signalstar_client.set_publisher(self.bus.publish)
                logger.info("Set publisher for SignalStar client")

            self.clients["signalstar"] = signalstar_client

        # Initialize models
        self.registered_models = {}

        # RSI Model
        if self.config.get("enable_rsi_model", True):
            self.rsi_model = RSIModel(
                period=self.config.get("rsi_period", 14),
                overbought_threshold=self.config.get("rsi_overbought", 70),
                oversold_threshold=self.config.get("rsi_oversold", 30)
            )
            self.registered_models["rsi"] = self.rsi_model

        # OrderFlow Model
        if self.config.get("enable_orderflow_model", True):
            self.orderflow_model = OrderFlowNet(
                model_path=self.config.get("orderflow_model_path", "models/orderflow_net.pt"),
                device=self.config.get("device", "cpu"),
                dummy_mode=self.config.get("dummy_orderflow", True)
            )
            self.registered_models["orderflow"] = self.orderflow_model

        # Volatility Regime Model
        if self.config.get("enable_volatility_regime_model", False):
            from models.volatility_regime import VolatilityRegimeModel
            self.volatility_model = VolatilityRegimeModel(
                config=self.config.get("volatility_regime_config", {})
            )
            self.registered_models["volatility_regime"] = self.volatility_model

        # VWAP Deviation Model
        if self.config.get("enable_vwap_deviation_model", False):
            from models.vwap_deviation import VWAPDeviationModel
            self.vwap_deviation_model = VWAPDeviationModel(
                config=self.config.get("vwap_deviation_config", {})
            )
            self.registered_models["vwap_deviation"] = self.vwap_deviation_model

        # Liquidity Imbalance Model
        if self.config.get("enable_liquidity_imbalance_model", False):
            from models.liquidity_imbalance import LiquidityImbalanceModel
            self.liquidity_imbalance_model = LiquidityImbalanceModel(
                config=self.config.get("liquidity_imbalance_config", {})
            )
            self.registered_models["liquidity_imbalance"] = self.liquidity_imbalance_model

        # GARCH Volatility Model
        if self.config.get("enable_garch_volatility_model", False):
            from models.garch_volatility import GARCHVolatilityModel
            self.garch_volatility_model = GARCHVolatilityModel(
                config=self.config.get("garch_volatility_config", {})
            )
            self.registered_models["garch_volatility"] = self.garch_volatility_model

        # Funding Momentum Model
        if self.config.get("enable_funding_momentum_model", False):
            from models.funding_momentum import FundingMomentumModel
            self.funding_momentum_model = FundingMomentumModel(
                config=self.config.get("funding_momentum_config", {})
            )
            self.registered_models["funding_momentum"] = self.funding_momentum_model

        # Open Interest Momentum Model
        if self.config.get("enable_open_interest_momentum_model", False):
            from models.open_interest_momentum import OpenInterestMomentumModel
            self.open_interest_momentum_model = OpenInterestMomentumModel(
                config=self.config.get("open_interest_momentum_config", {})
            )
            self.registered_models["open_interest_momentum"] = self.open_interest_momentum_model

        # Social Sentiment Model
        if self.config.get("enable_social_sentiment_model", False):
            from models.social_sentiment import SocialSentimentModel
            if "signalstar" in self.clients:
                social_sentiment_config = self.config.get("social_sentiment_config", {})
                self.social_sentiment_model = SocialSentimentModel(
                    client=self.clients["signalstar"],
                    delta_window=social_sentiment_config.get("delta_window", 5),
                    z_window=social_sentiment_config.get("z_window", 60),
                    threshold=social_sentiment_config.get("threshold", 1.2),
                    contrarian=social_sentiment_config.get("contrarian", True),
                    symbol=self.symbols[0] if self.symbols else "BTC-USDT",
                    config=social_sentiment_config
                )
                self.registered_models["social_sentiment"] = self.social_sentiment_model
            else:
                logger.warning("SignalStar client not initialized, social sentiment model disabled")

        # Meta-Ensemble Model
        if self.config.get("enable_ensemble_model", False):
            from models.meta_ensemble import MetaEnsembleModel
            self.meta_ensemble_model = MetaEnsembleModel(
                config=self.config.get("ensemble_model_config", {})
            )
            self.registered_models["meta_ensemble"] = self.meta_ensemble_model

        # LLM Bridge is now integrated into Enhanced LLM Consumer
        # Keeping reference for backward compatibility
        self.llm_bridge = None

        # Initialize Enhanced LLM Consumer
        try:
            self.llm_consumer = EnhancedLLMConsumer(
                bus=self.bus,
                feature_store=feature_store,
                config=self.config
            )
            logger.info("✅ Enhanced LLM Consumer initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize LLM Consumer: {e}")
            logger.warning("⚠️ LLM functionality will be disabled")
            self.llm_consumer = None

        # Initialize rule engine
        self.rule_engine = RuleEngine(self.config)

        # Initialize executor
        self.executor = HTXExecutor(
            client=self.htx_client,
            config=self.config,
            simulation_mode=self.simulation_mode
        )

        # Initialize position manager
        from position_manager import PositionManager
        self.position_manager = PositionManager(
            executor=self.executor,
            config=self.config
        )

        # Set position manager on executor
        self.executor.position_manager = self.position_manager

    async def start(self) -> None:
        """Start the orchestrator and all components."""
        logger.info("Starting orchestrator...")

        # Try to connect to primary data source (Multi-Exchange) first
        connection_success = await self._try_primary_connection()

        if not connection_success:
            logger.warning("⚠️ Primary Multi-Exchange connection failed, attempting Binance fallback...")
            fallback_success = await self._try_fallback_connection()

            if not fallback_success:
                logger.warning("⚠️ Binance fallback also failed, attempting mock data fallback...")
                mock_success = await self._try_mock_fallback()

                if not mock_success:
                    raise Exception("❌ All data sources failed - no data source available")

                logger.info("✅ Using mock data as final fallback")
                self.using_fallback = True
                self.using_mock = True
            else:
                logger.info("✅ Using Binance as fallback data source")
                self.using_fallback = True
                self.using_mock = False

                # Subscribe to Binance fallback data through message bus
                await self._setup_fallback_subscriptions()
        else:
            logger.info("✅ Using Multi-Exchange as primary data source")

            # Subscribe to Multi-Exchange data through message bus
            await self._setup_fallback_subscriptions()
            self.using_fallback = False

        # Fetch initial funding rate data if funding momentum model is enabled
        for symbol in self.symbols:
            if self.config.get("enable_funding_momentum_model", False):
                try:
                    # Fetch historical funding rates
                    funding_rates = await fetch_historical_funding_rates(
                        symbol,
                        size=60,  # Get up to 60 historical rates
                        testnet=self.config.get("testnet", False)
                    )

                    # Store in feature store
                    for event in funding_rates:
                        # Use dictionary access instead of attribute access
                        rate = event.get("rate")
                        if rate is None:
                            rate = event.get("funding_rate")
                            if rate is None:
                                logger.warning(f"Neither 'rate' nor 'funding_rate' found in funding data: {event}")
                                rate = 0.0
                            else:
                                logger.debug(f"Using 'funding_rate' key for funding data: {rate}")
                        else:
                            logger.debug(f"Using 'rate' key for funding data: {rate}")

                        # Convert rate to float
                        try:
                            rate = float(rate)
                        except (TypeError, ValueError):
                            logger.error(f"Could not convert funding rate to float: {rate}")
                            rate = 0.0

                        # Handle timestamp
                        timestamp = event.get("timestamp")
                        if isinstance(timestamp, str):
                            try:
                                timestamp = datetime.fromisoformat(timestamp)
                            except ValueError:
                                logger.warning(f"Could not parse timestamp: {timestamp}")
                                timestamp = datetime.now()
                        elif timestamp is None:
                            logger.warning("No timestamp found in funding data")
                            timestamp = datetime.now()

                        # Store in feature store
                        await feature_store.set(symbol, "funding_rate", rate)
                        await feature_store.add_time_series(
                            symbol, "funding_rates", rate, timestamp
                        )

                        logger.debug(f"Stored funding rate for {symbol}: {rate} at {timestamp}")

                    logger.info(f"Loaded {len(funding_rates)} historical funding rates for {symbol}")
                except Exception as e:
                    logger.error(f"Failed to fetch historical funding rates: {e}")

        # Start Enhanced LLM Consumer
        if self.llm_consumer:
            try:
                await self.llm_consumer.start()
                logger.info("✅ Enhanced LLM Consumer started successfully")
                logger.info(f"🧠 LLM Model: {self.config.get('model_path', 'Unknown')}")
            except Exception as e:
                logger.error(f"❌ Failed to start LLM Consumer: {e}")
                logger.warning("⚠️ Continuing without LLM functionality")
                self.llm_consumer = None
        else:
            logger.warning("⚠️ LLM Consumer not available - continuing without LLM functionality")

        # Start position manager
        await self.position_manager.start()
        logger.info("Position manager started")

        # Set running flag
        self.running = True

        # Start main loop
        await self._run_event_loop()

    async def stop(self) -> None:
        """Stop the orchestrator and all components."""
        logger.info("Stopping orchestrator...")

        # Set running flag
        self.running = False

        # Stop position manager
        await self.position_manager.stop()
        logger.info("Position manager stopped")

        # Stop Enhanced LLM Consumer
        await self.llm_consumer.stop()
        logger.info("Enhanced LLM Consumer stopped")

        # Close connections
        if self.using_fallback:
            await self.binance_client.close()
        else:
            await self.multi_exchange_client.close()

        # Close message bus
        if hasattr(self.bus, 'close'):
            self.bus.close()
            logger.info("Message bus closed")

        logger.info("Orchestrator stopped")

    async def _try_primary_connection(self) -> bool:
        """Try to connect to primary Multi-Exchange data source."""
        try:
            logger.info("🔄 Attempting Multi-Exchange connection...")

            # Connect to Multi-Exchange client
            if await self.multi_exchange_client.connect():
                logger.info("✅ Connected to Multi-Exchange client")
                return True
            else:
                logger.error("❌ Multi-Exchange connection failed")
                return False

        except Exception as e:
            logger.error(f"❌ Multi-Exchange connection failed: {e}")
            return False

    async def _try_fallback_connection(self) -> bool:
        """Try to connect to Binance fallback data source."""
        try:
            logger.info("🔄 Attempting Binance fallback connection...")

            # Connect to Binance
            if await self.binance_client.connect():
                logger.info("✅ Connected to Binance WebSocket (fallback)")
                return True
            else:
                logger.error("❌ Binance fallback connection failed")
                return False

        except Exception as e:
            logger.error(f"❌ Binance fallback failed: {e}")
            return False

    async def _setup_fallback_subscriptions(self) -> None:
        """Set up message bus subscriptions for Binance fallback data."""
        try:
            # Subscribe to fallback data topics
            self.bus.subscribe("htx.kline", self._on_fallback_kline)
            self.bus.subscribe("htx.trade", self._on_fallback_trade)
            self.bus.subscribe("htx.orderbook", self._on_fallback_orderbook)

            logger.info("✅ Set up message bus subscriptions for Binance fallback data")

        except Exception as e:
            logger.error(f"❌ Failed to set up fallback subscriptions: {e}")

    def _safe_create_task(self, coro):
        """Safely create an asyncio task, even if called from a sync context."""
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(coro)
        except RuntimeError:
            # No running loop, fallback to running directly (for debugging only)
            asyncio.run(coro)

    def _on_fallback_kline(self, timestamp: float, payload: dict) -> None:
        """Handle kline data from Binance fallback."""
        try:
            tick = payload.get("tick", {})
            channel = payload.get("ch", "")
            symbol_match = channel.split(".")[1] if "." in channel else "BTC-USDT"

            kline = Kline(
                symbol=symbol_match,
                timestamp=datetime.fromtimestamp(tick.get("id", time.time())),
                open=tick.get("open", 0.0),
                high=tick.get("high", 0.0),
                low=tick.get("low", 0.0),
                close=tick.get("close", 0.0),
                volume=tick.get("amount", 0.0),
                interval="1s",
                trades=tick.get("count", 0),
                closed=True
            )
            self._safe_create_task(self._handle_kline(kline))
        except Exception as e:
            logger.error(f"Error processing fallback kline: {e}")

    def _on_fallback_trade(self, timestamp: float, payload: dict) -> None:
        """Handle trade data from Binance fallback."""
        try:
            # Convert HTX-format payload to Trade object
            tick = payload.get("tick", {})
            data = tick.get("data", [])

            if not data:
                return

            trade_data = data[0]  # Get first trade

            # Extract symbol from channel
            channel = payload.get("ch", "")
            symbol_match = channel.split(".")[1] if "." in channel else "BTC-USDT"

            trade = Trade(
                symbol=symbol_match,
                price=trade_data.get("price", 0.0),
                quantity=trade_data.get("amount", 0.0),
                side=Side.BUY if trade_data.get("direction") == "buy" else Side.SELL,
                timestamp=datetime.fromtimestamp(trade_data.get("ts", timestamp) / 1000)
            )

            # Schedule the async handler
            self._safe_create_task(self._handle_trade(trade))

        except Exception as e:
            logger.error(f"Error processing fallback trade: {e}")

    def _on_fallback_orderbook(self, timestamp: float, payload: dict) -> None:
        """Handle orderbook data from Binance fallback."""
        try:
            # Convert HTX-format payload to OrderbookDelta object
            tick = payload.get("tick", {})

            # Extract symbol from channel
            channel = payload.get("ch", "")
            symbol_match = channel.split(".")[1] if "." in channel else "BTC-USDT"

            # Convert bid/ask arrays to OrderbookLevel objects
            from core.events import OrderbookLevel

            bids = [OrderbookLevel(price=float(bid[0]), quantity=float(bid[1]))
                   for bid in tick.get("bids", [])]
            asks = [OrderbookLevel(price=float(ask[0]), quantity=float(ask[1]))
                   for ask in tick.get("asks", [])]

            orderbook = OrderbookDelta(
                symbol=symbol_match,
                bids=bids,
                asks=asks,
                timestamp=datetime.fromtimestamp(timestamp)
            )

            # Schedule the async handler
            self._safe_create_task(self._handle_orderbook(orderbook))

        except Exception as e:
            logger.error(f"Error processing fallback orderbook: {e}")

    async def _run_event_loop(self) -> None:
        """Run the main event loop."""
        logger.info("Starting event loop")

        # Create tasks
        market_task = asyncio.create_task(self._process_market_data())
        private_task = asyncio.create_task(self._process_private_data())
        feature_task = asyncio.create_task(self._update_features())
        signal_task = asyncio.create_task(self._generate_signals())

        # Create account info task
        account_task = asyncio.create_task(self._update_account_info_periodically())

        # Create health check task
        health_check_task = asyncio.create_task(self._run_health_checks())

        # Create position manager task for price monitoring
        position_task = asyncio.create_task(self._monitor_positions())

        # Create debug signal generator task if enabled
        debug_task = None
        debug_config = self.config.get("debug", {})
        if debug_config.get("enabled", False) and debug_config.get("generate_test_signals", False):
            debug_task = asyncio.create_task(self._generate_debug_signals())
            logger.info("Started debug signal generator task")

        # Create funding rate task if funding momentum model is enabled
        funding_task = None
        if self.config.get("enable_funding_momentum_model", False):
            funding_task = asyncio.create_task(self._fetch_funding_rates())

        # Create open interest task if open interest momentum model is enabled
        open_interest_task = None
        if self.config.get("enable_open_interest_momentum_model", False):
            open_interest_task = asyncio.create_task(self._fetch_open_interest())

        # Create social sentiment task if social sentiment model is enabled
        sentiment_task = None
        if self.config.get("enable_social_sentiment_model", False) and "signalstar" in self.clients:
            sentiment_task = asyncio.create_task(self._fetch_social_sentiment())

        # Create bus maintenance task if using SQLiteBus
        bus_maintenance_task = None
        if hasattr(self.bus, 'cleanup_old_messages'):
            bus_maintenance_task = asyncio.create_task(self._bus_maintenance())
            logger.info("Started bus maintenance task")

        # Collect all tasks
        tasks = [market_task, private_task, feature_task, signal_task, account_task, health_check_task, position_task]
        if funding_task:
            tasks.append(funding_task)
        if open_interest_task:
            tasks.append(open_interest_task)
        if sentiment_task:
            tasks.append(sentiment_task)
        if bus_maintenance_task:
            tasks.append(bus_maintenance_task)
        if debug_task:
            tasks.append(debug_task)

        # Wait for tasks to complete
        try:
            await asyncio.gather(*tasks)
        except asyncio.CancelledError:
            logger.info("Event loop cancelled")
        except Exception as e:
            logger.error(f"Error in event loop: {e}")
        finally:
            # Cancel tasks
            for task in tasks:
                task.cancel()

    async def _process_market_data(self) -> None:
        """Process market data from WebSocket or message bus."""
        while self.running:
            try:
                if self.using_fallback:
                    # When using Binance fallback, data comes through the message bus
                    # The Binance client publishes to topics like "htx.kline", "htx.trade", etc.
                    # We'll process these through the bus subscription mechanism
                    await asyncio.sleep(0.1)  # Small sleep to avoid busy waiting
                else:
                    # Get next message from HTX client with timeout
                    message = await self.htx_client.get_next_market_message(timeout=1.0)

                    if message:
                        # Process message based on type
                        if isinstance(message, Kline):
                            await self._handle_kline(message)
                        elif isinstance(message, Trade):
                            await self._handle_trade(message)
                        elif isinstance(message, OrderbookDelta):
                            await self._handle_orderbook(message)

            except asyncio.TimeoutError:
                # Timeout is expected, continue
                pass
            except Exception as e:
                logger.error(f"Error processing market data: {e}")
                await asyncio.sleep(1.0)

    async def _process_private_data(self) -> None:
        """Process private data from WebSocket."""
        while self.running:
            try:
                # Get next message with timeout
                message = await self.htx_client.get_next_private_message(timeout=1.0)

                if message:
                    # Process message based on type
                    if isinstance(message, Position):
                        await self._handle_position(message)

            except asyncio.TimeoutError:
                # Timeout is expected, continue
                pass
            except Exception as e:
                logger.error(f"Error processing private data: {e}")
                await asyncio.sleep(1.0)

    async def _update_features(self) -> None:
        """Update features periodically."""
        while self.running:
            try:
                now = datetime.now()

                # Check if it's time to update features
                if now - self.last_feature_update >= self.feature_update_interval:
                    # Update features for each symbol
                    for symbol in self.symbols:
                        await self._calculate_features(symbol)

                    self.last_feature_update = now

                # Sleep to avoid busy waiting
                await asyncio.sleep(0.1)

            except Exception as e:
                logger.error(f"Error updating features: {e}")
                await asyncio.sleep(1.0)

    async def _generate_signals(self) -> None:
        """Generate trading signals periodically."""
        while self.running:
            try:
                # Generate signals for each symbol
                for symbol in self.symbols:
                    # Check if we should generate a signal
                    if await self._should_generate_signal(symbol):
                        # Generate signal
                        signal = await self._create_signal(symbol)

                        if signal:
                            # Execute signal if trading is enabled
                            if self.trading_enabled:
                                await self._execute_signal(signal)
                            else:
                                logger.info(f"Trading disabled, not executing signal: {signal.action} {symbol}")

                # Sleep to avoid busy waiting
                await asyncio.sleep(1.0)

            except Exception as e:
                logger.error(f"Error generating signals: {e}")
                await asyncio.sleep(5.0)

    async def _handle_kline(self, kline: Kline) -> None:
        """
        Handle a new kline.

        Args:
            kline: Kline data
        """
        symbol = kline.symbol

        # Store in feature store
        await feature_store.set(symbol, "kline", kline)
        await feature_store.set(symbol, "close_price", kline.close)
        await feature_store.set(symbol, "high_price", kline.high)
        await feature_store.set(symbol, "low_price", kline.low)

        # Add to time series
        await feature_store.add_time_series(symbol, "close_prices", kline.close, kline.timestamp)

        # Update last kline timestamp
        self.last_kline_timestamps[symbol] = kline.timestamp

        # Log
        logger.debug(f"Kline: {symbol} {kline.interval} {kline.timestamp} {kline.close}")

    async def _handle_trade(self, trade: Trade) -> None:
        """
        Handle a new trade.

        Args:
            trade: Trade data
        """
        symbol = trade.symbol

        # Store in feature store
        await feature_store.set(symbol, "last_trade", trade)
        await feature_store.set(symbol, "last_price", trade.price)

        # Add to time series
        await feature_store.add_time_series(symbol, "trades", trade, trade.timestamp)

        # Update last trade timestamp
        self.last_trade_timestamps[symbol] = trade.timestamp

    async def _handle_orderbook(self, orderbook: OrderbookDelta) -> None:
        """
        Handle an orderbook update.

        Args:
            orderbook: Orderbook data
        """
        symbol = orderbook.symbol

        # Store in feature store
        await feature_store.set(symbol, "orderbook", orderbook)

        # Calculate and store bid/ask prices
        if orderbook.bids and orderbook.asks:
            best_bid = orderbook.bids[0].price
            best_ask = orderbook.asks[0].price
            mid_price = (best_bid + best_ask) / 2
            spread = best_ask - best_bid

            await feature_store.set(symbol, "best_bid", best_bid)
            await feature_store.set(symbol, "best_ask", best_ask)
            await feature_store.set(symbol, "mid_price", mid_price)
            await feature_store.set(symbol, "spread", spread)
            await feature_store.set(symbol, "bid_ask_spread", spread)

            # Store top levels volumes
            if len(orderbook.bids) >= 3 and len(orderbook.asks) >= 3:
                await feature_store.set(symbol, "bid_volume_1", orderbook.bids[0].quantity)
                await feature_store.set(symbol, "ask_volume_1", orderbook.asks[0].quantity)
                await feature_store.set(symbol, "bid_volume_2", orderbook.bids[1].quantity)
                await feature_store.set(symbol, "ask_volume_2", orderbook.asks[1].quantity)
                await feature_store.set(symbol, "bid_volume_3", orderbook.bids[2].quantity)
                await feature_store.set(symbol, "ask_volume_3", orderbook.asks[2].quantity)

    async def _handle_position(self, position: Position) -> None:
        """
        Handle a position update.

        Args:
            position: Position data
        """
        symbol = position.symbol

        # Store in feature store
        await feature_store.set(symbol, "position", position)
        await feature_store.set(symbol, "position_side", position.side.value)
        await feature_store.set(symbol, "position_size", position.size)
        await feature_store.set(symbol, "position_entry_price", position.entry_price)

        # Track active positions
        if position.size > 0:
            self.active_positions.add(symbol)
        else:
            self.active_positions.discard(symbol)

    async def _fetch_funding_rates(self) -> None:
        """Periodically fetch funding rates for all symbols."""
        logger.info("Starting funding rate fetching task")

        # Get funding rate fetch interval from config (default: 60 seconds)
        interval = self.config.get("funding_rate_interval", 60)

        while self.running:
            try:
                for symbol in self.symbols:
                    # Fetch current funding rate
                    funding_event = await fetch_funding_rate(
                        symbol,
                        testnet=self.config.get("testnet", False)
                    )

                    if funding_event:
                        # Store in feature store (use dict access, not attribute)
                        await feature_store.set(symbol, "funding_rate", funding_event.get("rate"))
                        await feature_store.set(symbol, "estimated_funding_rate", funding_event.get("estimated_rate"))
                        await feature_store.set(symbol, "next_funding_time", funding_event.get("next_funding_time"))

                        # Store in time series
                        await feature_store.add_time_series(
                            symbol, "funding_rates", funding_event.get("rate"), funding_event.get("timestamp")
                        )

                        logger.debug(f"Updated funding rate for {symbol}: {funding_event.get('rate')}")

                        # If funding momentum model is enabled, update predictions
                        if "funding_momentum" in self.registered_models:
                            features = {
                                "symbol": symbol,
                                "timestamp": datetime.now()
                            }

                            # Get prediction
                            prediction = await self.registered_models["funding_momentum"].predict(features)

                            # Store prediction in feature store
                            await feature_store.set(symbol, "funding_momentum_prediction", prediction)

                            # Log significant signals
                            if prediction["signal"] != "NEUTRAL" and abs(prediction["funding_delta_z"]) > 1.0:
                                logger.info(
                                    f"Funding momentum signal for {symbol}: {prediction['signal']} "
                                    f"(z-score: {prediction['funding_delta_z']:.2f})"
                                )

            except Exception as e:
                logger.error(f"Error fetching funding rates: {e}")

            # Wait for next interval
            await asyncio.sleep(interval)

    async def _fetch_open_interest(self) -> None:
        """Periodically fetch open interest for all symbols."""
        logger.info("Starting open interest fetching task")

        # Get open interest fetch interval from config (default: 60 seconds)
        interval = self.config.get("open_interest_interval", 60)

        while self.running:
            try:
                for symbol in self.symbols:
                    # Fetch current open interest
                    oi_event = await fetch_open_interest(
                        symbol,
                        testnet=self.config.get("testnet", False)
                    )

                    if oi_event:
                        # Store in feature store (use attribute if dataclass, else dict)
                        open_interest = oi_event.open_interest if hasattr(oi_event, 'open_interest') else oi_event.get("open_interest")
                        amount = oi_event.amount if hasattr(oi_event, 'amount') else oi_event.get("amount")
                        timestamp = oi_event.timestamp if hasattr(oi_event, 'timestamp') else oi_event.get("timestamp")
                        await feature_store.set(symbol, "open_interest", open_interest)
                        await feature_store.set(symbol, "open_interest_amount", amount)
                        await feature_store.add_time_series(
                            symbol, "open_interest_history", open_interest, timestamp
                        )
                        logger.debug(f"Updated open interest for {symbol}: {open_interest}")

                        # If open interest momentum model is enabled, update predictions
                        if "open_interest_momentum" in self.registered_models:
                            features = {
                                "symbol": symbol,
                                "timestamp": datetime.now()
                            }
                            prediction = await self.registered_models["open_interest_momentum"].predict(features)
                            await feature_store.set(symbol, "open_interest_momentum_prediction", prediction)
                            if prediction["signal"] != "NEUTRAL" and abs(prediction["open_interest_delta_z"]) > 1.0:
                                logger.info(
                                    f"Open interest momentum signal for {symbol}: {prediction['signal']} "
                                    f"(z-score: {prediction['open_interest_delta_z']:.2f})"
                                )
            except Exception as e:
                logger.error(f"Error fetching open interest: {e}")
            await asyncio.sleep(interval)

    async def _fetch_social_sentiment(self) -> None:
        """Periodically fetch social sentiment data for all symbols."""
        logger.info("Starting social sentiment fetching task")

        # Get sentiment fetch interval from config (default: 60 seconds)
        interval = self.config.get("social_sentiment_interval", 60)

        while self.running:
            try:
                for symbol in self.symbols:
                    # Fetch current sentiment
                    client = self.clients.get("signalstar")
                    if not client:
                        logger.error("SignalStar client not available")
                        break

                    # Get latest sentiment
                    sentiment = await client.get_latest_sentiment(symbol)

                    # Store in feature store
                    await feature_store.set(symbol, "social.raw", sentiment)
            except Exception as e:
                logger.error(f"Error fetching social sentiment: {e}")

            await asyncio.sleep(interval)

    async def _bus_maintenance(self) -> None:
        """Perform periodic maintenance on the message bus."""
        # Only SQLiteBus or OptimizedSQLiteBus need maintenance
        if not hasattr(self.bus, 'cleanup_old_messages'):
            logger.info("Bus maintenance not needed for this bus type")
            return

        cleanup_interval = self.config.get("pipeline", {}).get("cleanup_interval_hours", 24)
        retention_days = self.config.get("pipeline", {}).get("message_retention_days", 7)

        logger.info(f"Bus maintenance scheduled every {cleanup_interval} hours, keeping messages for {retention_days} days")

        while self.running:
            try:
                # Sleep first to avoid immediate cleanup on startup
                await asyncio.sleep(cleanup_interval * 3600)

                # Clean up old messages
                deleted = self.bus.cleanup_old_messages(max_age_days=retention_days)
                logger.info(f"Bus maintenance: deleted {deleted} old messages")

            except Exception as e:
                logger.error(f"Error in bus maintenance: {e}")
                await asyncio.sleep(60)  # Sleep for a minute on error

    async def _generate_debug_signals(self) -> None:
        """Generate debug signals for testing."""
        # Get debug configuration
        debug_config = self.config.get("debug", {})
        interval = debug_config.get("test_signal_interval", 60)
        symbols = debug_config.get("test_signal_symbols", ["BTC-USDT"])

        # Fixed signal parameters
        direction = "SELL"
        score = -0.85
        source = "volatility"

        logger.info(f"Debug signal generator started with interval {interval}s")

        while self.running:
            try:
                for symbol in symbols:
                    # Get current price
                    try:
                        price = await feature_store.get(symbol, "last_price") or 60000.0
                    except Exception as e:
                        logger.warning(f"Error getting price from feature store: {e}")
                        price = 60000.0

                    # Create signal with fixed parameters
                    signal = Signal(
                        symbol=symbol,
                        action=Side.SELL,
                        score=score,
                        source=source,
                        timestamp=datetime.now(),
                        rationale=f"Debug signal from {source}",
                        metadata={
                            "price": price,
                            "debug": True
                        }
                    )

                    # Convert signal to dict and ensure datetime is serialized
                    signal_dict = signal_to_dict(signal)
                    self.bus.publish(f"signals.{source}", time.time(), signal_dict)

                    # Also publish a fused signal to trigger LLM
                    fused_signal = {
                        "symbol": symbol,
                        "direction": direction,
                        "score": score,
                        "source": "fused",
                        "timestamp": datetime.now().isoformat(),
                        "rationale": f"Fused debug signal (source: {source})",
                        "metadata": {
                            "price": price,
                            "debug": True,
                            "original_source": source,
                            "order_size": 0.001  # Specify a small order size
                        }
                    }
                    self.bus.publish("signals.fused", time.time(), fused_signal)

                    logger.info(f"Published debug signal: {direction} {symbol} with score {score} from {source}")

                # Wait for the next interval
                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"Error generating debug signals: {e}")
                await asyncio.sleep(10)

    async def _calculate_features(self, symbol: str) -> None:
        """
        Calculate derived features for a symbol.

        Args:
            symbol: Trading symbol
        """
        try:
            # Get raw data
            close_prices = await self._get_close_prices(symbol)

            if not close_prices or len(close_prices) < 15:
                return

            # Calculate RSI
            rsi_values = self.rsi_model._calculate_rsi(close_prices)
            current_rsi = float(rsi_values[-1])

            # Store RSI
            await feature_store.set(symbol, "rsi", current_rsi)

            # Calculate VWAP
            # (Simplified - in a real system we'd need volume data)
            vwap = close_prices[-15:].mean()
            vwap_dev = (close_prices[-1] - vwap) / vwap

            # Store VWAP
            await feature_store.set(symbol, "vwap", vwap)
            await feature_store.set(symbol, "vwap_dev", vwap_dev)

            # Calculate price changes
            price_change_1min = 0.0
            if len(close_prices) >= 60:
                price_change_1min = (close_prices[-1] - close_prices[-60]) / close_prices[-60]

            await feature_store.set(symbol, "price_change_1min", price_change_1min)

            # Calculate trade flow
            # (Simplified - in a real system we'd analyze actual trades)
            trade_flow_1min = 0.0
            trade_flow_5min = 0.0

            await feature_store.set(symbol, "trade_flow_1min", trade_flow_1min)
            await feature_store.set(symbol, "trade_flow_5min", trade_flow_5min)

            # Log
            logger.debug(f"Features calculated for {symbol}: RSI={current_rsi:.2f}, VWAP_dev={vwap_dev:.4f}")

        except Exception as e:
            logger.error(f"Error calculating features for {symbol}: {e}")

    async def _get_close_prices(self, symbol: str) -> Optional[List[float]]:
        """
        Get close prices for a symbol.

        Args:
            symbol: Trading symbol

        Returns:
            List of close prices or None
        """
        # Get from time series
        close_prices_series = await feature_store.get_time_series(symbol, "close_prices")

        if not close_prices_series:
            return None

        # Extract prices
        close_prices = [price for _, price in close_prices_series]

        return close_prices

    async def _should_generate_signal(self, symbol: str) -> bool:
        """
        Determine if we should generate a signal for a symbol.

        Args:
            symbol: Trading symbol

        Returns:
            True if we should generate a signal
        """
        # Check if we have enough data (reduced from 15 to 3 for faster startup)
        close_prices = await self._get_close_prices(symbol)
        if not close_prices or len(close_prices) < 3:
            logger.debug(f"Not enough price data for {symbol}: {len(close_prices) if close_prices else 0} prices")
            return False

        # Check if we have a position
        position = await feature_store.get(symbol, "position")

        # Check if we've called the LLM recently
        now = datetime.now()
        last_call = self.last_llm_call_time.get(symbol, datetime.min)
        time_since_last_call = (now - last_call).total_seconds()

        # Generate signal if:
        # 1. We have a position and it's been at least 30 seconds since last call
        # 2. We don't have a position and it's been at least 15 seconds since last call
        # 3. We've never generated a signal for this symbol
        if position and time_since_last_call < 30:  # 30 seconds (reduced from 5 minutes)
            return False
        elif not position and time_since_last_call < 15:  # 15 seconds (reduced from 1 minute)
            return False

        # Check if we have too many positions
        if len(self.active_positions) >= self.max_positions and symbol not in self.active_positions:
            return False

        # Check for significant price movement
        if len(close_prices) >= 60:
            price_change = (close_prices[-1] - close_prices[-60]) / close_prices[-60]
            if abs(price_change) < 0.001:  # 0.1% change
                return False

        return True

    async def _create_signal(self, symbol: str) -> Optional[Signal]:
        """
        Create a trading signal for a symbol.

        Args:
            symbol: Trading symbol

        Returns:
            Trading signal or None
        """
        # Check if smart strategy is enabled
        strategy_config = self.config.get("strategy", {})
        strategy_type = strategy_config.get("type", "smart_model_integrated_strategy")
        use_smart_strategy = strategy_config.get("use_smart_strategy", False) or strategy_type == "smart_model_integrated_strategy"

        logger.debug(f"Strategy config: type={strategy_type}, use_smart={use_smart_strategy}")

        if use_smart_strategy:
            return await self._create_smart_signal(symbol)
        else:
            return await self._create_model_signal(symbol)

    async def _create_smart_signal(self, symbol: str) -> Optional[Signal]:
        """
        Create a trading signal using the Smart Model-Integrated Strategy.

        Args:
            symbol: Symbol to create signal for

        Returns:
            Signal object or None
        """
        try:
            # Import the smart strategy
            from backtester.strategies import smart_model_integrated_strategy

            # Generate signals using our best-performing strategy
            signals = await smart_model_integrated_strategy(
                timestamp=datetime.now(),
                symbols=[symbol],
                signal_source="smart_integrated"
            )

            # Return the first signal if available
            if signals:
                signal = signals[0]
                logger.info(f"🎯 Smart strategy generated signal: {signal.action} {symbol} with score {signal.score:.3f}")
                logger.info(f"📝 Rationale: {signal.rationale}")
                return signal
            else:
                logger.debug(f"Smart strategy generated no signals for {symbol}")
                return None

        except Exception as e:
            logger.error(f"Error creating smart signal for {symbol}: {e}")
            # Fallback to model-based signal generation
            return await self._create_model_signal(symbol)

    async def _create_model_signal(self, symbol: str) -> Optional[Signal]:
        """
        Create a trading signal using individual models (fallback method).

        Args:
            symbol: Trading symbol

        Returns:
            Trading signal or None
        """
        # Get features
        features = await feature_store.get_snapshot(symbol)
        if not features:
            return None

        # Get close prices
        close_prices = await self._get_close_prices(symbol)
        if not close_prices:
            return None

        # Add close prices to features
        features["close_prices"] = close_prices
        features["symbol"] = symbol

        # Dictionary to store all model predictions
        model_predictions = {}

        # Start time for performance monitoring
        start_time = datetime.now()

        # Run all registered models
        for model_name, model in self.registered_models.items():
            try:
                # Measure model execution time
                model_start_time = datetime.now()
                prediction = await model.predict(features)
                model_execution_time = (datetime.now() - model_start_time).total_seconds()

                # Store prediction in feature store with model namespace
                await feature_store.set(symbol, f"{model_name}_prediction", prediction)

                # Store in our local dictionary for signal creation
                model_predictions[model_name] = prediction

                # Store metrics
                await feature_store.set(symbol, f"metrics.{model_name}.latency", model_execution_time)
                await feature_store.set(symbol, f"metrics.{model_name}.last_run", datetime.now().isoformat())

                logger.debug(f"Model {model_name} execution time: {model_execution_time:.4f}s")
            except Exception as e:
                logger.error(f"Error running model {model_name}: {e}")
                # Use empty dict if model fails
                model_predictions[model_name] = {}

        # For backward compatibility
        rsi_prediction = model_predictions.get("rsi", {})
        orderflow_prediction = model_predictions.get("orderflow", {})

        # Prepare context for LLM
        context = {
            "symbol": symbol,
            "ts": datetime.now().isoformat(),
            "mid": features.get("mid_price", 0.0),
            "rsi": rsi_prediction.get("rsi", 50.0),
            "vwap_dev": features.get("vwap_dev", 0.0),
            "delta_60": orderflow_prediction.get("delta_price_60s", 0.0),
            "prob_ob": rsi_prediction.get("prob_overbought", 0.0),
            "pos_side": features.get("position_side", "NONE"),
            "pos_size": features.get("position_size", 0.0)
        }

        # Add additional model outputs to LLM context if available
        # Volatility Regime Model
        if "volatility_regime" in model_predictions:
            vol_pred = model_predictions["volatility_regime"]
            if "regime" in vol_pred and "confidence" in vol_pred:
                context["vol_regime"] = vol_pred["regime"]
                context["vol_conf"] = vol_pred["confidence"]

        # VWAP Deviation Model
        if "vwap_deviation" in model_predictions:
            vwap_pred = model_predictions["vwap_deviation"]
            if "primary_z_score" in vwap_pred and "signal" in vwap_pred:
                context["primary_z_score"] = round(vwap_pred["primary_z_score"], 2)
                context["vwap_signal"] = vwap_pred["signal"]

        # Liquidity Imbalance Model
        if "liquidity_imbalance" in model_predictions:
            liq_pred = model_predictions["liquidity_imbalance"]
            if "imbalance_ratio" in liq_pred and "signal" in liq_pred:
                context["imbalance_ratio"] = round(liq_pred["imbalance_ratio"], 2)
                context["liquidity_signal"] = liq_pred["signal"]
                context["liquidity_thinness"] = round(liq_pred.get("thinness", 0.0), 2)

        # GARCH Volatility Model
        if "garch_volatility" in model_predictions:
            garch_pred = model_predictions["garch_volatility"]
            if "volatility_forecast" in garch_pred and "volatility_level" in garch_pred:
                context["volatility_forecast"] = round(garch_pred["volatility_forecast"], 4)
                context["volatility_level"] = garch_pred["volatility_level"]
                context["volatility_z"] = round(garch_pred["volatility_z"], 2)
                context["position_size_multiplier"] = round(garch_pred["position_size_multiplier"], 2)

        # Funding Momentum Model
        if "funding_momentum" in model_predictions:
            funding_pred = model_predictions["funding_momentum"]
            if "funding_rate" in funding_pred and "funding_delta_z" in funding_pred:
                context["funding_rate"] = round(funding_pred["funding_rate"], 4)
                context["funding_delta"] = round(funding_pred.get("funding_delta", 0.0), 4)
                context["funding_delta_z"] = round(funding_pred["funding_delta_z"], 2)
                context["funding_signal"] = funding_pred["signal"]
                context["funding_window"] = funding_pred.get("short_window", 5)

        # Open Interest Momentum Model
        if "open_interest_momentum" in model_predictions:
            oi_pred = model_predictions["open_interest_momentum"]
            if "open_interest" in oi_pred and "open_interest_delta_z" in oi_pred:
                context["open_interest"] = int(oi_pred["open_interest"])
                context["open_interest_delta"] = int(oi_pred.get("open_interest_delta", 0.0))
                context["open_interest_delta_z"] = round(oi_pred["open_interest_delta_z"], 2)
                context["open_interest_signal"] = oi_pred["signal"]
                context["open_interest_window"] = oi_pred.get("delta_window", 5)

        # Social Sentiment Model
        if "social_sentiment" in model_predictions:
            ss_pred = model_predictions["social_sentiment"]
            if "sentiment" in ss_pred and "z_score" in ss_pred:
                context["social_sentiment"] = round(ss_pred["sentiment"], 2)
                context["social_delta"] = round(ss_pred.get("delta", 0.0), 2)
                context["social_z_score"] = round(ss_pred["z_score"], 2)
                context["social_signal"] = ss_pred["signal"]
                context["social_contrarian"] = ss_pred.get("contrarian", True)

        # Meta-Ensemble Model
        if "meta_ensemble" in model_predictions:
            ensemble_pred = model_predictions["meta_ensemble"]
            context["ensemble_action"] = ensemble_pred.get("ensemble_action", "HOLD")
            context["ensemble_score"] = round(ensemble_pred.get("ensemble_score", 0.0), 2)
            context["ensemble_confidence_lower"] = round(ensemble_pred.get("confidence_interval", {}).get("lower", 0.0), 2)
            context["ensemble_confidence_upper"] = round(ensemble_pred.get("confidence_interval", {}).get("upper", 0.0), 2)
            context["ensemble_total_models"] = ensemble_pred.get("total_models", 0)

            # Add model weights if available
            if "model_weights" in ensemble_pred:
                context["ensemble_weights"] = ensemble_pred["model_weights"]

        # Call LLM
        llm_response = await self.llm_bridge.generate(context)

        # Update last call time
        self.last_llm_call_time[symbol] = datetime.now()

        # Create signals from predictions
        signals = []

        # Create signals from all model predictions
        for model_name, prediction in model_predictions.items():
            if model_name == "rsi" and prediction:
                # Create RSI signal
                rsi_signal = Signal(
                    symbol=symbol,
                    timestamp=datetime.now(),
                    action=Side.BUY if prediction.get("signal_strength", 0.0) > 0.3 else
                           Side.SELL if prediction.get("signal_strength", 0.0) < -0.3 else
                           Side.HOLD,
                    score=prediction.get("signal_strength", 0.0),
                    source=model_name,
                    rationale=f"RSI: {prediction.get('rsi', 0.0):.1f}, " +
                              f"OB: {prediction.get('is_overbought', False)}, " +
                              f"OS: {prediction.get('is_oversold', False)}",
                    metadata={"price": features.get("mid_price", 0.0)}
                )
                signals.append(rsi_signal)

            elif model_name == "orderflow" and prediction:
                # Create OrderFlow signal
                orderflow_signal = Signal(
                    symbol=symbol,
                    timestamp=datetime.now(),
                    action=Side.BUY if prediction.get("direction", "") == "up" else
                           Side.SELL if prediction.get("direction", "") == "down" else
                           Side.HOLD,
                    score=prediction.get("delta_price_60s", 0.0) * 10,  # Scale to [-1, 1]
                    source=f"ml.{model_name}",
                    rationale=f"ΔPrice_60s: {prediction.get('delta_price_60s', 0.0):.4f}, " +
                              f"Conf: {prediction.get('confidence', 0.0):.2f}",
                    metadata={"price": features.get("mid_price", 0.0)}
                )
                signals.append(orderflow_signal)

            # Volatility Regime Model
            elif model_name == "volatility_regime" and prediction:
                # Create volatility regime signal if it has a trading action
                if "action" in prediction and "confidence" in prediction:
                    vol_signal = Signal(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        action=Side(prediction["action"]) if prediction["action"] in ["BUY", "SELL", "HOLD"] else Side.HOLD,
                        score=prediction.get("confidence", 0.0) * (1 if prediction.get("action") == "BUY" else -1 if prediction.get("action") == "SELL" else 0),
                        source=model_name,
                        rationale=f"Regime: {prediction.get('regime', 'UNKNOWN')}, Conf: {prediction.get('confidence', 0.0):.2f}",
                        metadata={"price": features.get("mid_price", 0.0), "regime": prediction.get("regime", "UNKNOWN")}
                    )
                    signals.append(vol_signal)

            # VWAP Deviation Model
            elif model_name == "vwap_deviation" and prediction:
                # Create VWAP deviation signal
                if "action" in prediction and "confidence" in prediction:
                    vwap_signal = Signal(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        action=Side(prediction["action"]) if prediction["action"] in ["BUY", "SELL", "HOLD"] else Side.HOLD,
                        score=prediction.get("confidence", 0.0) * (1 if prediction.get("action") == "BUY" else -1 if prediction.get("action") == "SELL" else 0),
                        source=model_name,
                        rationale=f"Z-score: {prediction.get('primary_z_score', 0.0):.2f}, Signal: {prediction.get('signal', 'UNKNOWN')}",
                        metadata={
                            "price": features.get("mid_price", 0.0),
                            "z_score": prediction.get("primary_z_score", 0.0),
                            "vwap": prediction.get("vwap", {}).get("1min", 0.0)
                        }
                    )
                    signals.append(vwap_signal)

            # Liquidity Imbalance Model
            elif model_name == "liquidity_imbalance" and prediction:
                # Create liquidity imbalance signal
                if "action" in prediction and "confidence" in prediction:
                    liq_signal = Signal(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        action=Side(prediction["action"]) if prediction["action"] in ["BUY", "SELL", "HOLD"] else Side.HOLD,
                        score=prediction.get("confidence", 0.0) * (1 if prediction.get("action") == "BUY" else -1 if prediction.get("action") == "SELL" else 0),
                        source=model_name,
                        rationale=f"Imbalance: {prediction.get('imbalance_ratio', 0.0):.2f}, Signal: {prediction.get('signal', 'UNKNOWN')}",
                        metadata={
                            "price": features.get("mid_price", 0.0),
                            "imbalance_ratio": prediction.get("imbalance_ratio", 0.0),
                            "thinness": prediction.get("thinness", 0.0)
                        }
                    )
                    signals.append(liq_signal)

            # GARCH Volatility Model
            elif model_name == "garch_volatility" and prediction:
                # Create GARCH volatility signal
                if "action" in prediction and "confidence" in prediction:
                    garch_signal = Signal(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        action=Side(prediction["action"]) if prediction["action"] in ["BUY", "SELL", "HOLD"] else Side.HOLD,
                        score=prediction.get("confidence", 0.0) * (1 if prediction.get("action") == "BUY" else -1 if prediction.get("action") == "SELL" else 0),
                        source=model_name,
                        rationale=f"Vol: {prediction.get('volatility_level', 'NORMAL')}, Z: {prediction.get('volatility_z', 0.0):.2f}",
                        metadata={
                            "price": features.get("mid_price", 0.0),
                            "volatility_forecast": prediction.get("volatility_forecast", 0.0),
                            "volatility_z": prediction.get("volatility_z", 0.0),
                            "position_size_multiplier": prediction.get("position_size_multiplier", 1.0),
                            "stop_width_multiplier": prediction.get("stop_width_multiplier", 1.0)
                        }
                    )
                    signals.append(garch_signal)

            # Funding Momentum Model
            elif model_name == "funding_momentum" and prediction:
                # Create funding momentum signal
                if "action" in prediction and "confidence" in prediction:
                    funding_signal = Signal(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        action=Side(prediction["action"]) if prediction["action"] in ["BUY", "SELL", "HOLD"] else Side.HOLD,
                        score=prediction.get("confidence", 0.0) * (1 if prediction.get("action") == "BUY" else -1 if prediction.get("action") == "SELL" else 0),
                        source=model_name,
                        rationale=f"Funding: {prediction.get('funding_rate', 0.0):.4f}, Delta Z: {prediction.get('funding_delta_z', 0.0):.2f}",
                        metadata={
                            "price": features.get("mid_price", 0.0),
                            "funding_rate": prediction.get("funding_rate", 0.0),
                            "funding_delta": prediction.get("funding_delta", 0.0),
                            "funding_delta_z": prediction.get("funding_delta_z", 0.0),
                            "signal": prediction.get("signal", "NEUTRAL")
                        }
                    )
                    signals.append(funding_signal)

            # Open Interest Momentum Model
            elif model_name == "open_interest_momentum" and prediction:
                # Create open interest momentum signal
                if "action" in prediction and "confidence" in prediction:
                    # Apply weight from model
                    weight = prediction.get("weight", 0.5)
                    confidence = prediction.get("confidence", 0.0) * weight

                    oi_signal = Signal(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        action=Side(prediction["action"]) if prediction["action"] in ["BUY", "SELL", "HOLD"] else Side.HOLD,
                        score=confidence * (1 if prediction.get("action") == "BUY" else -1 if prediction.get("action") == "SELL" else 0),
                        source=model_name,
                        rationale=f"OI: {prediction.get('open_interest', 0.0):.0f}, Delta Z: {prediction.get('open_interest_delta_z', 0.0):.2f}",
                        metadata={
                            "price": features.get("mid_price", 0.0),
                            "open_interest": prediction.get("open_interest", 0.0),
                            "open_interest_delta": prediction.get("open_interest_delta", 0.0),
                            "open_interest_delta_z": prediction.get("open_interest_delta_z", 0.0),
                            "signal": prediction.get("signal", "NEUTRAL"),
                            "mode": prediction.get("mode", "contrarian"),
                            "weight": weight
                        }
                    )
                    signals.append(oi_signal)

            # Social Sentiment Model
            elif model_name == "social_sentiment" and prediction:
                # Create social sentiment signal
                if "signal" in prediction and "confidence" in prediction:
                    # Apply weight based on volatility if available
                    weight = 0.5  # Default weight

                    # If we have volatility information, adjust weight
                    if "garch_volatility" in model_predictions:
                        vol_pred = model_predictions["garch_volatility"]
                        if "volatility_level" in vol_pred:
                            # Increase weight in high volatility, decrease in low
                            if vol_pred["volatility_level"] == "HIGH":
                                weight = 0.7
                            elif vol_pred["volatility_level"] == "LOW":
                                weight = 0.3

                    confidence = prediction.get("confidence", 0.0) * weight

                    ss_signal = Signal(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        action=Side(prediction["signal"]) if prediction["signal"] in ["BUY", "SELL", "HOLD"] else Side.HOLD,
                        score=confidence * (1 if prediction.get("signal") == "BUY" else -1 if prediction.get("signal") == "SELL" else 0),
                        source=model_name,
                        rationale=f"Sentiment: {prediction.get('sentiment', 0.0):.1f}, Delta Z: {prediction.get('z_score', 0.0):.2f}",
                        metadata={
                            "price": features.get("mid_price", 0.0),
                            "sentiment": prediction.get("sentiment", 0.0),
                            "delta": prediction.get("delta", 0.0),
                            "z_score": prediction.get("z_score", 0.0),
                            "signal": prediction.get("signal", "HOLD"),
                            "contrarian": prediction.get("contrarian", True),
                            "weight": weight
                        }
                    )
                    signals.append(ss_signal)

            # Create Meta-Ensemble signal
            elif model_name == "meta_ensemble" and prediction:
                # Extract ensemble action and score
                ensemble_action = prediction.get("ensemble_action", "HOLD")
                ensemble_score = prediction.get("ensemble_score", 0.0)

                # Calculate confidence from confidence interval
                confidence_interval = prediction.get("confidence_interval", {})
                lower = confidence_interval.get("lower", 0.0)
                upper = confidence_interval.get("upper", 0.0)
                confidence = (lower + upper) / 2.0 if lower != upper else lower

                # Create signal
                ensemble_signal = Signal(
                    symbol=symbol,
                    timestamp=datetime.now(),
                    action=Side(ensemble_action) if ensemble_action in ["BUY", "SELL", "HOLD"] else Side.HOLD,
                    score=ensemble_score,
                    source=model_name,
                    rationale=f"Ensemble of {prediction.get('total_models', 0)} models, confidence: {lower:.2f}-{upper:.2f}",
                    metadata={
                        "price": features.get("mid_price", 0.0),
                        "confidence_lower": lower,
                        "confidence_upper": upper,
                        "total_models": prediction.get("total_models", 0),
                        "contributing_models": prediction.get("contributing_models", [])
                    }
                )
                signals.append(ensemble_signal)

        # Create LLM signal
        llm_signal = Signal(
            symbol=symbol,
            timestamp=datetime.now(),
            action=llm_response.action,
            score=llm_response.score,
            source="llm",
            rationale=llm_response.rationale,
            metadata={"price": features.get("mid_price", 0.0)}
        )
        signals.append(llm_signal)

        # Fuse signals
        fused_signal = self.rule_engine.fuse_signals(signals, features)

        # Record total signal generation time
        total_time = (datetime.now() - start_time).total_seconds()
        await feature_store.set(symbol, "metrics.signal_generation_time", total_time)

        return fused_signal

    async def _monitor_positions(self) -> None:
        """Monitor positions for price updates."""
        logger.info("Starting position monitoring task")

        # Get monitoring interval from config (default: 1 second)
        interval = self.config.get("position_monitor_interval", 1.0)

        while self.running:
            try:
                # Check each symbol with active positions
                for symbol in self.symbols:
                    # Get current price
                    price = await feature_store.get(symbol, "last_price")
                    if price:
                        # Update position manager with current price
                        await self.position_manager.on_price_update(symbol, price)

                # Sleep to avoid busy waiting
                await asyncio.sleep(interval)

            except Exception as e:
                logger.error(f"Error in position monitor: {e}")
                await asyncio.sleep(5.0)

    async def _execute_signal(self, signal: Signal) -> None:
        """
        Execute a trading signal.

        Args:
            signal: Trading signal
        """
        # Log signal
        logger.info(f"Executing signal: {signal.action} {signal.symbol} (score: {signal.score:.2f})")

        # Execute signal
        response = await self.executor.execute(signal)

        if response:
            logger.info(f"Order placed: {response.order_id}")

            # Publish trade execution
            trade_execution = {
                "symbol": signal.symbol,
                "side": signal.action.value,
                "quantity": response.quantity,
                "price": response.price,
                "order_id": response.order_id,
                "timestamp": datetime.now().isoformat(),
                "source": signal.source,
                "rationale": signal.rationale,
                "pnl": 0.0,  # Will be updated when the position is closed
                "pnl_pct": 0.0  # Will be updated when the position is closed
            }

            # Add metadata if available
            if hasattr(signal, "metadata") and signal.metadata:
                trade_execution["metadata"] = signal.metadata

            # Publish to trades.executed stream
            self.bus.publish("trades.executed", time.time(), trade_execution)

            # Store in feature store
            await feature_store.set(signal.symbol, "last_trade_execution", trade_execution)
            await feature_store.add_time_series(signal.symbol, "trade_executions", trade_execution, datetime.now())

        else:
            logger.warning(f"Failed to place order for signal: {signal.action} {signal.symbol}")

    async def _update_account_info_periodically(self) -> None:
        """Periodically update account information."""
        logger.info("Starting account information update task")

        # Update interval (default: 30 seconds)
        interval = self.config.get("account_update_interval", 30)

        while self.running:
            try:
                # Update account information
                await self._update_account_info()

                # Wait for next interval
                await asyncio.sleep(interval)
            except Exception as e:
                logger.error(f"Error in account update task: {e}")
                await asyncio.sleep(5.0)

    async def _update_account_info(self) -> None:
        """Update account information."""
        try:
            # Get account information
            account_info = await self.htx_client.get_account_info()

            # Extract account data
            if "data" in account_info and isinstance(account_info["data"], list) and len(account_info["data"]) > 0:
                account_data = account_info["data"][0]
                self.account_balance = float(account_data.get("margin_balance", 0.0))
                self.available_balance = float(account_data.get("margin_available", 0.0))
            else:
                # Fallback to top-level keys if data structure is different
                self.account_balance = float(account_info.get("total_equity", 0.0))
                self.available_balance = float(account_info.get("available_balance", 0.0))

            logger.info(f"Account balance: {self.account_balance:.2f} USDT, Available: {self.available_balance:.2f} USDT")

            # Publish account state to the bus
            self.bus.publish("account.state", time.time(), {
                "total": self.account_balance,
                "available": self.available_balance,
                "reserved": self.account_balance - self.available_balance,
                "timestamp": datetime.now().isoformat()
            })

            # Get positions (with error handling for missing method)
            try:
                positions = await self.htx_client.get_positions()
            except AttributeError as e:
                logger.warning(f"HTX client missing get_positions method: {e}")
                positions = []

            # Publish positions state to the bus
            self.bus.publish("positions.state", time.time(), {
                "positions": positions,
                "timestamp": datetime.now().isoformat()
            })

            # Get open orders
            orders = await self.htx_client.get_open_orders()

            # Publish orders state to the bus
            self.bus.publish("orders.state", time.time(), {
                "orders": orders,
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Error updating account information: {e}")

    async def _on_llm_signal(self, _ts: float, payload: dict) -> None:
        """
        Handle an LLM signal.

        Args:
            _ts: Timestamp (unused)
            payload: Signal payload
        """
        try:
            # Extract signal data
            symbol = payload.get("symbol", self.symbols[0] if self.symbols else "BTC-USDT")
            action_str = payload.get("action", "HOLD")
            confidence = payload.get("confidence", 0.5)
            rationale = payload.get("rationale", "")

            # Validate the payload
            is_valid = self._validate_llm_payload(payload)

            # Get confidence threshold from config
            confidence_threshold = self.config.get("llm_confidence_threshold", 0.6)

            # Check if we should use this signal or fall back to fused signal
            if not is_valid:
                logger.warning(f"Invalid LLM payload: {payload}. Falling back to fused signal.")
                await self._fallback_to_fused_signal(symbol)
                return

            if confidence < confidence_threshold:
                logger.warning(f"LLM confidence ({confidence:.2f}) below threshold ({confidence_threshold:.2f}). Falling back to fused signal.")
                await self._fallback_to_fused_signal(symbol)
                return

            # Convert action string to Side enum
            if action_str.upper() == "BUY":
                action = Side.BUY
            elif action_str.upper() == "SELL":
                action = Side.SELL
            else:
                action = Side.HOLD

            # Calculate score based on action and confidence
            score = confidence * (1 if action == Side.BUY else -1 if action == Side.SELL else 0)

            # Create signal object
            signal = Signal(
                symbol=symbol,
                timestamp=datetime.now(),
                action=action,
                score=score,
                confidence=confidence,
                source="llm",
                rationale=rationale,
                metadata=payload.get("metadata", {})
            )

            # Log the signal
            logger.info(f"Received LLM signal: {action_str} {symbol} with confidence {confidence:.2f}")
            logger.info(f"Rationale: {rationale}")

            # Track LLM metrics
            await self._track_llm_metrics(payload, is_valid)

            # Execute signal if trading is enabled
            if self.trading_enabled:
                await self._execute_signal(signal)
            else:
                logger.info(f"Trading disabled, not executing LLM signal: {action_str} {symbol}")

        except Exception as e:
            logger.error(f"Error handling LLM signal: {e}")
            # Attempt to fall back to fused signal on error
            try:
                symbol = payload.get("symbol", self.symbols[0] if self.symbols else "BTC-USDT")
                await self._fallback_to_fused_signal(symbol)
            except Exception as fallback_error:
                logger.error(f"Error in fallback to fused signal: {fallback_error}")


    def _validate_llm_payload(self, payload: dict) -> bool:
        """
        Validate the LLM payload.

        Args:
            payload: LLM payload

        Returns:
            True if valid, False otherwise
        """
        # Check required fields
        if "action" not in payload:
            logger.warning("Missing 'action' in LLM payload")
            return False

        if "confidence" not in payload:
            logger.warning("Missing 'confidence' in LLM payload")
            return False

        if "rationale" not in payload:
            logger.warning("Missing 'rationale' in LLM payload")
            return False

        # Validate action
        action = payload["action"]
        if action not in ["BUY", "SELL", "HOLD"]:
            logger.warning(f"Invalid action '{action}' in LLM payload")
            return False

        # Validate confidence
        confidence = payload["confidence"]
        if not isinstance(confidence, (int, float)) or confidence < 0 or confidence > 1:
            logger.warning(f"Invalid confidence '{confidence}' in LLM payload")
            return False

        return True

    async def _fallback_to_fused_signal(self, symbol: str) -> None:
        """
        Fall back to the most recent fused signal.

        Args:
            symbol: Trading symbol
        """
        logger.info(f"Falling back to fused signal for {symbol}")

        try:
            # Get the most recent fused signal from feature store
            fused_signal = await feature_store.get(symbol, "fused_signal")

            if not fused_signal:
                logger.warning(f"No fused signal available for {symbol}")
                return

            # Create signal object
            signal = Signal(
                symbol=symbol,
                timestamp=datetime.now(),
                action=Side(fused_signal.get("action", "HOLD")),
                score=fused_signal.get("score", 0.0),
                confidence=fused_signal.get("confidence", 0.5),
                source="fused_fallback",
                rationale=f"Fallback from LLM: {fused_signal.get('rationale', 'No rationale')}",
                metadata=fused_signal.get("metadata", {})
            )

            # Log the signal
            logger.info(f"Using fallback fused signal: {signal.action} {symbol} with confidence {signal.confidence:.2f}")

            # Execute signal if trading is enabled
            if self.trading_enabled:
                await self._execute_signal(signal)
            else:
                logger.info(f"Trading disabled, not executing fallback signal: {signal.action} {symbol}"

        except Exception as e:
            logger.error(f"Error in fallback to fused signal: {e}")

    async def _run_health_checks(self) -> None:
        """Run periodic health checks."""
        logger.info("Starting health check task")

        # Health check interval (default: 60 seconds)
        interval = self.config.get("health_check_interval", 60)

        # Alert thresholds
        llm_error_threshold = self.config.get("llm_error_threshold", 0.05)  # 5%
        llm_latency_threshold = self.config.get("llm_latency_threshold", 5.0)  # 5 seconds

        while self.running:
            try:
                # Wait for next interval
                await asyncio.sleep(interval)

                # Run health checks for each symbol
                for symbol in self.symbols:
                    await self._check_llm_health(symbol, llm_error_threshold, llm_latency_threshold)

            except Exception as e:
                logger.error(f"Error in health check task: {e}")
                await asyncio.sleep(5.0)

    async def _check_llm_health(self, symbol: str, error_threshold: float, latency_threshold: float) -> None:
        """
        Check LLM health for a symbol.

        Args:
            symbol: Trading symbol
            error_threshold: Error rate threshold for alerts
            latency_threshold: Latency threshold for alerts
        """
        try:
            # Get LLM metrics from feature store
            call_count = await feature_store.get(symbol, "metrics.llm.call_count") or 0
            # Only get invalid calls for error rate calculation
            invalid_calls = await feature_store.get(symbol, "metrics.llm.invalid_calls") or 0

            # Get recent latency values
            latency_series = await feature_store.get_time_series(
                symbol, "metrics.llm.latency",
                start=datetime.now() - timedelta(minutes=10)
            )

            # Calculate error rate
            if call_count > 0:
                error_rate = invalid_calls / call_count
            else:
                error_rate = 0.0

            # Calculate average latency
            if latency_series:
                latencies = [value for _, value in latency_series]
                avg_latency = sum(latencies) / len(latencies)
            else:
                avg_latency = 0.0

            # Log health metrics
            logger.info(f"LLM health for {symbol}: calls={call_count}, error_rate={error_rate:.2%}, avg_latency={avg_latency:.2f}s")

            # Check for alerts
            if error_rate > error_threshold:
                logger.warning(f"LLM error rate ({error_rate:.2%}) exceeds threshold ({error_threshold:.2%}) for {symbol}")

            if avg_latency > latency_threshold:
                logger.warning(f"LLM average latency ({avg_latency:.2f}s) exceeds threshold ({latency_threshold:.2f}s) for {symbol}")

            # Store health metrics
            await feature_store.set(symbol, "metrics.llm.health.error_rate", error_rate)
            await feature_store.set(symbol, "metrics.llm.health.avg_latency", avg_latency)

        except Exception as e:
            logger.error(f"Error checking LLM health for {symbol}: {e}")

    async def _track_llm_metrics(self, payload: dict, is_valid: bool) -> None:
        """
        Track LLM metrics.

        Args:
            payload: LLM payload
            is_valid: Whether the payload is valid
        """
        try:
            # Get symbol
            symbol = payload.get("symbol", self.symbols[0] if self.symbols else "BTC-USDT")

            # Track call count
            await feature_store.increment(symbol, "metrics.llm.call_count", 1)

            # Track valid/invalid calls
            if is_valid:
                await feature_store.increment(symbol, "metrics.llm.valid_calls", 1)
            else:
                await feature_store.increment(symbol, "metrics.llm.invalid_calls", 1)

            # Track confidence
            confidence = payload.get("confidence", 0.0)
            await feature_store.add_time_series(symbol, "metrics.llm.confidence", confidence, datetime.now())

            # Track action distribution
            action = payload.get("action", "HOLD")
            await feature_store.increment(symbol, f"metrics.llm.actions.{action}", 1)

            # Track latency if available
            if "latency" in payload:
                latency = payload.get("latency", 0.0)
                await feature_store.add_time_series(symbol, "metrics.llm.latency", latency, datetime.now())

        except Exception as e:
            logger.error(f"Error tracking LLM metrics: {e}")


async def run_orchestrator(config: Dict[str, Any]) -> None:
    """
    Run the orchestrator.

    Args:
        config: Configuration dictionary
    """
    # Set up logging
    setup_logging(level=logging.INFO)

    # Create orchestrator
    orchestrator = Orchestrator(config)

    # Start orchestrator
    try:
        await orchestrator.start()
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")
    finally:
        # Stop orchestrator
        await orchestrator.stop()


if __name__ == "__main__":
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Smart Trader Orchestrator")
    parser.add_argument("--strategy", default="smart_integrated", help="Strategy to use")
    parser.add_argument("--symbol", default="BTC-USDT", help="Symbol to trade")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--testnet", action="store_true", help="Use testnet")
    parser.add_argument("--trading", action="store_true", help="Enable live trading")
    args = parser.parse_args()

    # Default configuration
    config = {
        # Exchange settings
        "symbols": [args.symbol],
        "api_key": "",
        "api_secret": "",
        "testnet": args.testnet,

        # Trading settings
        "trading_enabled": args.trading,
        "simulation_mode": not args.trading,
        "max_positions": 1,
        "risk_per_trade": 0.01,
        "max_position_size": 1.0,
        "default_leverage": 10,
        "contract_size": 0.001,
        "sim_balance": 10000.0,

        # Model toggles
        "enable_rsi_model": True,
        "enable_orderflow_model": True,
        "enable_volatility_regime_model": True,
        "enable_vwap_deviation_model": True,
        "enable_liquidity_imbalance_model": True,
        "enable_garch_volatility_model": True,
        "enable_funding_momentum_model": True,
        "enable_open_interest_momentum_model": True,
        "enable_social_sentiment_model": True,    # SignalStar social sentiment
        "enable_ensemble_model": True,            # Meta-Ensemble model

        # RSI model settings
        "rsi_period": 14,
        "rsi_overbought": 70,
        "rsi_oversold": 30,

        # OrderFlow model settings
        "orderflow_model_path": "models/orderflow_net.pt",
        "dummy_orderflow": True,

        # Volatility regime model settings
        "volatility_regime_config": {
            "lookback_window": 30,
            "regime_thresholds": {
                "low_vol": 0.5,
                "high_vol": 2.0
            }
        },

        # VWAP deviation model settings
        "vwap_deviation_config": {
            "lookback_periods": 100,
            "significant_deviation": 2.0,
            "vwap_types": ["1min", "5min", "15min", "1h"]
        },

        # Liquidity imbalance model settings
        "liquidity_imbalance_config": {
            "window_size": 20,
            "depth_levels": 5,
            "significant_imbalance": 0.3,
            "decay_factor": 0.9
        },

        # GARCH volatility model settings
        "garch_volatility_config": {
            "lookback_periods": 100,
            "update_interval": 300,  # 5 minutes in seconds
            "long_term_window": 500,
            "garch_p": 1,
            "garch_q": 1,
            "model_type": "GARCH",  # "GARCH", "EGARCH", or "GJR-GARCH"
            "vol_thresholds": {
                "very_low": -1.5,
                "low": -0.5,
                "high": 0.5,
                "very_high": 1.5
            }
        },

        # Funding momentum model settings
        "funding_momentum_config": {
            "short_window": 5,      # 5 minutes
            "long_window": 60,      # 60 minutes
            "signal_threshold": 1.0,  # Z-score threshold for signals
            "contrarian": True      # Whether to use contrarian signals
        },

        # Open interest momentum model settings
        "open_interest_momentum_config": {
            "delta_window": 5,      # 5 minutes
            "z_score_window": 60,   # 60 minutes
            "threshold_z": 1.0,     # Z-score threshold for signals
            "mode": "contrarian",   # "trend" or "contrarian"
            "base_weight": 0.5,     # Base weight for signal fusion
            "boost_in_low_volatility": True  # Double weight in low volatility
        },

        # Social sentiment model settings
        "social_sentiment_config": {
            "delta_window": 5,      # 5 minutes
            "z_window": 60,         # 60 minutes
            "threshold": 1.2,       # Z-score threshold for signals
            "contrarian": True,     # Whether to use contrarian signals
            "symbol": "BTC"         # Default symbol for sentiment data
        },

        # Meta-Ensemble model settings
        "ensemble_model_config": {
            "performance_window": 24,  # 24 hours
            "min_weight": 0.1,         # Minimum weight for any model
            "max_weight": 3.0,         # Maximum weight for any model
            "learning_rate": 0.05,     # Rate at which weights are updated
            "model_weights": {         # Initial weights for models
                "rsi": 1.0,
                "orderflow": 1.5,
                "volatility_regime": 1.2,
                "vwap_deviation": 1.0,
                "liquidity_imbalance": 1.0,
                "garch_volatility": 1.3,
                "funding_momentum": 1.2,
                "open_interest_momentum": 1.1,
                "social_sentiment": 0.8
            }
        },

        # SignalStar client settings
        "signalstar": {
            "api_key": "",          # Add your SignalStar API key here
            "base_url": "https://api.signalstar.com/v1"
        },

        # Social sentiment fetching interval
        "social_sentiment_interval": 60,  # 60 seconds

        # Data fetching settings
        "funding_rate_interval": 60,  # 60 seconds
        "open_interest_interval": 60,  # 60 seconds

        # LLM settings
        "llm_model_path": r"C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf",
        "llm_prompt_path": "llm/prompts/trading_prompt.yaml",
        "llm_threads": 4,
        "llm_gpu_layers": 0,
        "dummy_llm": True,  # Temporarily disable LLM for testing

        # Performance monitoring
        "enable_metrics": True,
        "metrics_retention_days": 7,

        # Debug settings
        "debug": {
            "enabled": args.debug,
            "generate_test_signals": False,
            "test_signal_interval": 60
        }
    }

    # Set up logging level based on debug flag
    if args.debug:
        setup_logging(level=logging.DEBUG)
        logger.info(f"🐛 Debug mode enabled")
        logger.info(f"📊 Strategy: {args.strategy}")
        logger.info(f"💰 Symbol: {args.symbol}")
        logger.info(f"🧪 Testnet: {args.testnet}")
        logger.info(f"💸 Trading: {args.trading}")
    else:
        setup_logging(level=logging.INFO)

    # Run orchestrator
    asyncio.run(run_orchestrator(config))
