#!/usr/bin/env python3
"""
Mock Data Producer for Smart-Trader Dashboard Testing

This script generates mock market data and feeds it into the SQLite bus
for testing the dashboard when real market data is not available.
"""

import asyncio
import logging
import time
import random
import json
from datetime import datetime
from pipeline.databus import SQLiteBus

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MockDataProducer:
    """Produces mock market data for testing."""

    def __init__(self):
        self.bus = None
        self.running = False
        self.symbols = ["BTC-USDT", "ETH-USDT"]
        self.prices = {
            "BTC-USDT": 45000.0,
            "ETH-USDT": 3000.0
        }

    async def start(self):
        """Start the mock data producer."""
        logger.info("🚀 Starting Mock Data Producer for testing...")

        try:
            # Initialize SQLite bus
            self.bus = SQLiteBus(path="data/bus.db", poll_interval=0.5)
            self.running = True

            logger.info("✅ Mock Data Producer started")
            logger.info("📊 Generating mock market data...")

            # Generate data
            await self.generate_data()

        except Exception as e:
            logger.error(f"❌ Failed to start Mock Data Producer: {e}")
            await self.stop()

    async def generate_data(self):
        """Generate mock market data."""
        message_count = 0

        while self.running:
            try:
                for symbol in self.symbols:
                    # Generate mock price movement
                    price_change = random.uniform(-0.02, 0.02)  # ±2% change
                    self.prices[symbol] *= (1 + price_change)
                    
                    current_price = self.prices[symbol]
                    timestamp = time.time()

                    # Generate mock kline data
                    kline_data = {
                        "symbol": symbol,
                        "price": current_price,
                        "open": current_price * random.uniform(0.998, 1.002),
                        "high": current_price * random.uniform(1.0, 1.005),
                        "low": current_price * random.uniform(0.995, 1.0),
                        "close": current_price,
                        "volume": random.uniform(100, 1000),
                        "timestamp": timestamp,
                        "interval": "1s"
                    }

                    # Publish to bus
                    self.bus.publish(f"market.{symbol}.kline", timestamp, kline_data)

                    # Generate mock trade data
                    trade_data = {
                        "symbol": symbol,
                        "price": current_price,
                        "quantity": random.uniform(0.1, 10.0),
                        "side": random.choice(["buy", "sell"]),
                        "timestamp": timestamp
                    }

                    self.bus.publish(f"market.{symbol}.trade", timestamp, trade_data)

                    message_count += 2

                # Log status every 100 messages
                if message_count % 100 == 0:
                    logger.info(f"📈 Generated {message_count} mock messages")

                await asyncio.sleep(1)  # Generate data every second

            except Exception as e:
                logger.error(f"❌ Error generating mock data: {e}")
                await asyncio.sleep(5)

    async def stop(self):
        """Stop the mock data producer."""
        logger.info("🛑 Stopping Mock Data Producer...")
        self.running = False

        if self.bus:
            try:
                self.bus.close()
                logger.info("✅ SQLite bus closed")
            except Exception as e:
                logger.error(f"Error closing bus: {e}")

        logger.info("🏁 Mock Data Producer stopped")

async def main():
    """Main function."""
    producer = MockDataProducer()

    try:
        await producer.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    finally:
        await producer.stop()

if __name__ == "__main__":
    asyncio.run(main())
