{"strategy_name": "Order Flow", "test_start_time": "2025-05-28T23:50:22.924416", "database_connection": true, "strategy_startup": true, "process_running": true, "data_flow": {"recent_messages": 0, "orderflow_messages": 0, "dataframe_messages": 0, "market_messages": 0, "signal_messages": 0, "data_flowing": false}, "components": {"dataframe_runner": true, "order_flow_analysis": false, "signal_generation": false, "market_data_feed": false}, "strategy_shutdown": true, "overall_success": false, "test_duration": 45, "test_end_time": "2025-05-28T23:51:13.816992"}